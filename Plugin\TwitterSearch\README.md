# TwitterSearch Plugin

基于Twitter API v2的推特数据获取和搜索插件，为VCP系统提供强大的社交媒体数据分析能力。

## 功能特性

### 🔍 推文搜索
- 支持关键词、话题标签、用户搜索
- 高级搜索语法支持（AND、OR、NOT等）
- 时间范围筛选
- 返回详细的推文和用户信息

### 👤 用户信息查询
- 获取用户详细资料
- 粉丝数、关注数、推文数统计
- 认证状态、位置、个人网站等信息

### 📝 用户推文获取
- 获取指定用户的最新推文
- 可排除转推和回复
- 支持批量获取

## API限制

### 免费版本
- **月度限制**: 500,000推文/月
- **搜索范围**: 最近7天的推文
- **速率限制**: 300请求/15分钟窗口

### 搜索语法示例

```
# 基础搜索
"artificial intelligence"
"machine learning"

# 话题标签
#AI
#MachineLearning
#OpenAI

# 组合搜索
#AI OR #MachineLearning
"AI" -crypto
"OpenAI" AND "GPT"

# 用户相关
from:elonmusk
to:openai
@username

# 语言筛选
lang:en
lang:zh

# 互动筛选
min_retweets:100
min_faves:50
```

## 配置说明

1. 复制 `config.env.example` 为 `config.env`
2. 访问 [Twitter Developer Portal](https://developer.twitter.com/)
3. 申请开发者账户（免费）
4. 创建新的App项目
5. 生成Bearer Token
6. 将Token填入配置文件

## 使用示例

### 搜索AI相关推文
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」TwitterSearch「末」,
command:「始」search_tweets「末」,
query:「始」#AI OR "artificial intelligence"「末」,
max_results:「始」20「末」
<<<[END_TOOL_REQUEST]>>>
```

### 获取用户信息
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」TwitterSearch「末」,
command:「始」get_user_info「末」,
username:「始」openai「末」
<<<[END_TOOL_REQUEST]>>>
```

### 获取用户推文
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」TwitterSearch「末」,
command:「始」get_user_tweets「末」,
username:「始」elonmusk「末」,
max_results:「始」15「末」,
exclude:「始」retweets,replies「末」
<<<[END_TOOL_REQUEST]>>>
```

## 返回数据格式

### 推文搜索结果
```json
{
  "tweets": [
    {
      "id": "1234567890",
      "text": "推文内容...",
      "created_at": "2024-01-01T12:00:00.000Z",
      "author": {
        "username": "username",
        "name": "Display Name",
        "verified": true,
        "followers_count": 1000000
      },
      "metrics": {
        "retweet_count": 100,
        "like_count": 500,
        "reply_count": 50
      },
      "url": "https://twitter.com/username/status/1234567890"
    }
  ],
  "result_count": 20
}
```

### 用户信息结果
```json
{
  "username": "openai",
  "name": "OpenAI",
  "description": "用户简介...",
  "verified": true,
  "location": "San Francisco, CA",
  "metrics": {
    "followers_count": 5000000,
    "following_count": 100,
    "tweet_count": 2000
  },
  "profile_url": "https://twitter.com/openai"
}
```

## 应用场景

### 🔥 热点追踪
- 实时监控话题趋势
- 追踪突发事件讨论
- 分析公众舆论

### 📊 数据分析
- 用户行为分析
- 内容传播研究
- 社交网络分析

### 🤖 AI应用
- 情感分析训练数据
- 社交媒体机器人
- 内容推荐系统

### 📈 营销洞察
- 品牌提及监控
- 竞品分析
- 影响者识别

## 注意事项

1. **API密钥安全**: 请妥善保管Bearer Token，不要在代码中硬编码
2. **速率限制**: 合理控制请求频率，避免超出限制
3. **数据使用**: 遵守Twitter的使用条款和数据政策
4. **隐私保护**: 处理用户数据时注意隐私保护

## 错误处理

插件会自动处理常见错误：
- API密钥无效
- 速率限制超出
- 用户不存在
- 网络连接问题

错误信息会以标准格式返回，便于AI理解和处理。

## 依赖项

- Node.js >= 14.0.0
- axios >= 1.6.0

## 许可证

本插件遵循 CC BY-NC-SA 4.0 许可证。
