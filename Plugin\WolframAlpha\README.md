# Wolfram Alpha Plugin

这是一个用于 VCPToolBox 的 Wolfram Alpha API 集成插件，提供强大的计算知识引擎功能，支持复杂数学计算、科学查询、数据分析等。

## 功能特性

- 🧮 **数学计算**: 代数方程求解、微积分、统计分析
- 🔬 **科学查询**: 物理公式、化学反应、生物信息
- 🌍 **知识问答**: 历史事件、地理信息、人口统计
- 📊 **数据分析**: 图表生成、数据可视化
- 🔄 **单位转换**: 温度、长度、重量等各种单位转换
- 📈 **图表生成**: 自动生成数学图表和可视化图像
- 💾 **图像保存**: 可选择保存生成的图表到本地

## 配置说明

### 1. 获取 Wolfram Alpha API Key

1. 访问 [Wolfram Alpha API](https://products.wolframalpha.com/api/)
2. 注册开发者账号
3. 创建新应用
4. 获取 App ID

### 2. 配置插件

复制 `config.env.example` 为 `config.env` 并填写：

```env
# Wolfram Alpha API 配置
WOLFRAM_APP_ID=your_wolfram_app_id_here

# 输出格式配置
WOLFRAM_OUTPUT_FORMAT=plaintext,image

# 单位系统
WOLFRAM_UNITS=metric

# API 请求超时时间（秒）
WOLFRAM_TIMEOUT=20

# 是否保存图表和图像到本地
SAVE_IMAGES=true

# 图像保存路径
IMAGE_SAVE_PATH=image/wolfram
```

## 使用方法

### 数学计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」solve x^2 + 5x + 6 = 0「末」
<<<[END_TOOL_REQUEST]>>>
```

### 微积分

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」derivative of sin(x)*cos(x)「末」
<<<[END_TOOL_REQUEST]>>>
```

### 积分计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」integrate x^2 from 0 to 5「末」
<<<[END_TOOL_REQUEST]>>>
```

### 统计分析

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」mean of {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}「末」
<<<[END_TOOL_REQUEST]>>>
```

### 单位转换

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」convert 100 fahrenheit to celsius「末」
<<<[END_TOOL_REQUEST]>>>
```

### 科学查询

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」speed of light in vacuum「末」
<<<[END_TOOL_REQUEST]>>>
```

### 知识问答

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」population of China 2024「末」
<<<[END_TOOL_REQUEST]>>>
```

### 图表生成

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」plot sin(x) from -pi to pi「末」,
format:「始」plaintext,image「末」,
save_image:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

## 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `query` | string | ✅ | 要查询的问题或计算表达式 |
| `format` | string | ❌ | 输出格式 (默认: plaintext,image) |
| `units` | string | ❌ | 单位系统 (metric/imperial，默认: metric) |
| `save_image` | boolean | ❌ | 是否保存图像 (默认: true) |

## 支持的查询类型

### 数学类
- 代数方程求解
- 微积分 (导数、积分)
- 线性代数 (矩阵运算)
- 统计分析 (均值、方差、分布)
- 数论 (质数、因式分解)
- 几何计算

### 科学类
- 物理常数和公式
- 化学元素和反应
- 生物学信息
- 天文学数据
- 工程计算

### 实用工具类
- 单位转换
- 日期时间计算
- 金融计算 (利率、投资)
- 地理信息
- 人口统计

### 图表和可视化
- 函数图像
- 数据图表
- 几何图形
- 统计图表

## 返回数据格式

```json
{
  "status": "success",
  "result": {
    "query": "solve x^2 + 5x + 6 = 0",
    "success": true,
    "summary": "查询: solve x^2 + 5x + 6 = 0\n结果: x = -2 or x = -3",
    "pods": [
      {
        "title": "Input",
        "content": ["x^2 + 5 x + 6 = 0"],
        "images": []
      },
      {
        "title": "Solutions",
        "content": ["x = -2", "x = -3"],
        "images": [
          {
            "url": "https://www6a.wolframalpha.com/...",
            "local_path": "/path/to/image/wolfram_123456789_0.png",
            "filename": "wolfram_123456789_0.png"
          }
        ]
      }
    ]
  }
}
```

## 与 SciCalculator 的区别

| 特性 | SciCalculator | WolframAlpha |
|------|---------------|--------------|
| **计算能力** | 基础科学计算 | 高级知识引擎 |
| **查询方式** | 数学表达式 | 自然语言 + 表达式 |
| **知识范围** | 数学计算 | 数学 + 科学 + 常识 |
| **图表生成** | 无 | 支持 |
| **单位转换** | 无 | 支持 |
| **实时数据** | 无 | 支持 |
| **API 依赖** | 无 | 需要网络连接 |
| **使用限制** | 无限制 | 每月 2000 次查询 |

## 注意事项

1. **API 限制**: 免费账户每月 2000 次查询
2. **网络要求**: 需要稳定的网络连接
3. **查询语言**: 支持英文查询，中文查询可能不准确
4. **响应时间**: 复杂查询可能需要较长时间
5. **图像存储**: 启用图像保存时注意本地存储空间

## 故障排除

### 常见错误

1. **401 Unauthorized**: 检查 App ID 是否正确
2. **403 Quota Exceeded**: 超出每月查询限制
3. **Timeout**: 查询过于复杂，尝试简化问题
4. **No results**: 查询表达式可能不被理解，尝试重新表述

### 调试模式

可以通过查看 VCP 日志来调试插件运行状态。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础数学计算和科学查询
- 支持图表生成和图像保存
- 完整的错误处理机制
- 与 VCP 系统完全集成
