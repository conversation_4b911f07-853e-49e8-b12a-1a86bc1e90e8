const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setupPlugin() {
    console.log('🧮 Wolfram Alpha Plugin 安装向导');
    console.log('=' * 50);
    
    // 检查是否已有配置文件
    const configPath = path.join(__dirname, 'config.env');
    const exampleConfigPath = path.join(__dirname, 'config.env.example');
    
    if (fs.existsSync(configPath)) {
        console.log('⚠️  检测到已存在的配置文件');
        const overwrite = await question('是否要重新配置？(y/N): ');
        if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
            console.log('✅ 保持现有配置');
            rl.close();
            return;
        }
    }
    
    console.log('\n📋 请按照以下步骤配置插件：');
    console.log('1. 访问 https://products.wolframalpha.com/api/');
    console.log('2. 注册开发者账号（如果还没有）');
    console.log('3. 创建新应用');
    console.log('4. 获取 App ID');
    console.log('');
    
    // 获取 App ID
    const appId = await question('请输入您的 Wolfram Alpha App ID: ');
    if (!appId.trim()) {
        console.log('❌ App ID 不能为空');
        rl.close();
        return;
    }
    
    // 询问输出格式
    console.log('\n📊 选择输出格式:');
    console.log('1. plaintext - 纯文本结果');
    console.log('2. image - 图像结果');
    console.log('3. plaintext,image - 文本和图像（推荐）');
    console.log('4. mathml - 数学标记语言');
    
    const formatChoice = await question('请选择输出格式 (1-4, 默认3): ');
    const formatMap = {
        '1': 'plaintext',
        '2': 'image', 
        '3': 'plaintext,image',
        '4': 'mathml'
    };
    const outputFormat = formatMap[formatChoice] || 'plaintext,image';
    
    // 询问单位系统
    console.log('\n📏 选择单位系统:');
    console.log('1. metric - 公制单位（米、千克、摄氏度）');
    console.log('2. imperial - 英制单位（英尺、磅、华氏度）');
    
    const unitsChoice = await question('请选择单位系统 (1-2, 默认1): ');
    const units = unitsChoice === '2' ? 'imperial' : 'metric';
    
    // 询问是否保存图像
    const saveImages = await question('是否保存生成的图表和图像到本地？(Y/n): ');
    const shouldSaveImages = saveImages.toLowerCase() !== 'n' && saveImages.toLowerCase() !== 'no';
    
    // 设置保存路径
    let imagePath = 'image/wolfram';
    if (shouldSaveImages) {
        const customPath = await question(`图像保存路径 (默认: ${imagePath}): `);
        if (customPath.trim()) {
            imagePath = customPath.trim();
        }
    }
    
    // 设置超时时间
    const timeoutInput = await question('API 请求超时时间（秒，默认20）: ');
    const timeout = parseInt(timeoutInput) || 20;
    
    // 生成配置文件内容
    const configContent = `# Wolfram Alpha API 配置
# 获取 App ID: https://products.wolframalpha.com/api/
WOLFRAM_APP_ID=${appId}

# 输出格式配置
# 可选值: plaintext, image, mathml, sound
# 可以组合使用，用逗号分隔，如: plaintext,image
WOLFRAM_OUTPUT_FORMAT=${outputFormat}

# 单位系统
# metric: 公制单位 (米、千克、摄氏度等)
# imperial: 英制单位 (英尺、磅、华氏度等)
WOLFRAM_UNITS=${units}

# API 请求超时时间（秒）
WOLFRAM_TIMEOUT=${timeout}

# 是否保存图表和图像到本地 (true/false)
SAVE_IMAGES=${shouldSaveImages}

# 图像保存路径 (相对于项目根目录)
IMAGE_SAVE_PATH=${imagePath}
`;

    try {
        // 写入配置文件
        fs.writeFileSync(configPath, configContent, 'utf8');
        console.log('\n✅ 配置文件已创建: config.env');
        
        // 创建图像保存目录
        if (shouldSaveImages) {
            const fullImagePath = path.resolve(process.cwd(), '../../', imagePath);
            if (!fs.existsSync(fullImagePath)) {
                fs.mkdirSync(fullImagePath, { recursive: true });
                console.log(`✅ 图像保存目录已创建: ${fullImagePath}`);
            }
        }
        
        console.log('\n🎉 插件安装完成！');
        console.log('\n📖 使用说明：');
        console.log('1. 重启 VCP 服务器以加载插件');
        console.log('2. 在系统提示词中添加: {{VCPWolframAlpha}}');
        console.log('3. AI 现在可以使用 Wolfram Alpha 进行复杂计算了！');
        
        console.log('\n🧪 测试插件：');
        console.log('运行: node test.js');
        
        console.log('\n💡 功能特性：');
        console.log('- 数学计算和方程求解');
        console.log('- 微积分和统计分析');
        console.log('- 科学常数和公式查询');
        console.log('- 单位转换和数据分析');
        console.log('- 函数绘图和图表生成');
        console.log('- 知识问答和信息查询');
        
        console.log('\n⚠️  注意事项：');
        console.log('- 免费账户每月限制 2000 次查询');
        console.log('- 复杂查询可能需要较长时间');
        console.log('- 建议使用英文进行查询以获得最佳结果');
        
        console.log('\n📚 更多信息请查看 README.md');
        
    } catch (error) {
        console.log(`❌ 创建配置文件失败: ${error.message}`);
    }
    
    rl.close();
}

// 运行安装向导
if (require.main === module) {
    setupPlugin().catch(console.error);
}

module.exports = { setupPlugin };
