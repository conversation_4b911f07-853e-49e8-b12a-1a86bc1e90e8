const { spawn } = require('child_process');

// 简单测试
function testTwitterPlugin() {
    console.log('🧪 开始测试TwitterSearch插件...');
    
    const testData = {
        command: "search_tweets",
        query: "#AI",
        max_results: 5
    };
    
    const child = spawn('node', ['TwitterSearch.js'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: __dirname
    });
    
    let output = '';
    let error = '';
    
    child.stdout.on('data', (data) => {
        output += data.toString();
    });
    
    child.stderr.on('data', (data) => {
        error += data.toString();
    });
    
    child.on('close', (code) => {
        console.log(`\n📊 测试结果:`);
        console.log(`退出代码: ${code}`);
        
        if (error) {
            console.log(`❌ 错误信息:`, error);
        }
        
        if (output) {
            try {
                const result = JSON.parse(output.trim());
                console.log(`✅ 插件响应:`, JSON.stringify(result, null, 2));
                
                if (result.status === 'success') {
                    console.log(`🎉 测试成功！找到 ${result.result.result_count} 条推文`);
                    if (result.result.tweets && result.result.tweets.length > 0) {
                        console.log(`📝 第一条推文预览:`);
                        const firstTweet = result.result.tweets[0];
                        console.log(`   作者: @${firstTweet.author.username}`);
                        console.log(`   内容: ${firstTweet.text.substring(0, 100)}...`);
                        console.log(`   链接: ${firstTweet.url}`);
                    }
                } else {
                    console.log(`⚠️ 测试失败: ${result.error}`);
                }
            } catch (parseError) {
                console.log(`❌ JSON解析错误:`, parseError.message);
                console.log(`原始输出:`, output);
            }
        } else {
            console.log(`❌ 无输出`);
        }
    });
    
    child.on('error', (err) => {
        console.log(`❌ 进程错误:`, err.message);
    });
    
    // 发送测试数据
    child.stdin.write(JSON.stringify(testData));
    child.stdin.end();
}

// 运行测试
testTwitterPlugin();
