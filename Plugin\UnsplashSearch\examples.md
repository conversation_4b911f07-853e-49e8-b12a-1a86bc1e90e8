# Unsplash Search Plugin 使用示例

## 基础搜索示例

### 1. 简单关键词搜索

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」sunset「末」,
per_page:「始」5「末」
<<<[END_TOOL_REQUEST]>>>
```

### 2. 中文关键词搜索

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」城市夜景「末」,
per_page:「始」3「末」
<<<[END_TOOL_REQUEST]>>>
```

## 高级筛选示例

### 3. 按方向筛选 - 横向图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」mountain landscape「末」,
per_page:「始」4「末」,
orientation:「始」landscape「末」
<<<[END_TOOL_REQUEST]>>>
```

### 4. 按方向筛选 - 纵向图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」skyscraper「末」,
per_page:「始」3「末」,
orientation:「始」portrait「末」
<<<[END_TOOL_REQUEST]>>>
```

### 5. 按分类筛选 - 自然风景

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」forest「末」,
per_page:「始」5「末」,
category:「始」nature「末」,
orientation:「始」landscape「末」
<<<[END_TOOL_REQUEST]>>>
```

### 6. 按分类筛选 - 人物肖像

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」portrait「末」,
per_page:「始」4「末」,
category:「始」people「末」,
orientation:「始」portrait「末」
<<<[END_TOOL_REQUEST]>>>
```

### 7. 按颜色筛选 - 蓝色主调

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」ocean「末」,
per_page:「始」3「末」,
color:「始」blue「末」,
orientation:「始」landscape「末」
<<<[END_TOOL_REQUEST]>>>
```

### 8. 按颜色筛选 - 黑白照片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」architecture「末」,
per_page:「始」4「末」,
color:「始」black_and_white「末」
<<<[END_TOOL_REQUEST]>>>
```

## 组合筛选示例

### 9. 多条件组合 - 自然风景 + 绿色主调 + 横向

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」forest trees「末」,
per_page:「始」3「末」,
category:「始」nature「末」,
color:「始」green「末」,
orientation:「始」landscape「末」
<<<[END_TOOL_REQUEST]>>>
```

### 10. 多条件组合 - 商业场景 + 方形构图

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」office workspace「末」,
per_page:「始」4「末」,
category:「始」business「末」,
orientation:「始」squarish「末」
<<<[END_TOOL_REQUEST]>>>
```

## 下载功能示例

### 11. 搜索并下载图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」cute cat「末」,
per_page:「始」2「末」,
download:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

## 特定用途示例

### 12. 寻找背景图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」abstract background「末」,
per_page:「始」6「末」,
category:「始」backgrounds「末」,
orientation:「始」landscape「末」
<<<[END_TOOL_REQUEST]>>>
```

### 13. 寻找美食图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」delicious food「末」,
per_page:「始」5「末」,
category:「始」food「末」
<<<[END_TOOL_REQUEST]>>>
```

### 14. 寻找科技相关图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」technology innovation「末」,
per_page:「始」4「末」,
category:「始」computer「末」
<<<[END_TOOL_REQUEST]>>>
```

### 15. 寻找旅行图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」travel destination「末」,
per_page:「始」5「末」,
category:「始」travel「末」,
orientation:「始」landscape「末」
<<<[END_TOOL_REQUEST]>>>
```

## AI 使用建议

当 AI 使用这个插件时，建议：

1. **根据用户需求选择合适的关键词**
2. **合理设置图片数量** (通常3-8张比较合适)
3. **根据用途选择方向** (横向适合背景，纵向适合肖像)
4. **利用分类筛选提高精准度**
5. **在回复中展示图片** 使用 `<img>` 标签
6. **提供作者信息** 尊重摄影师版权
7. **附上 Unsplash 链接** 方便用户访问原图

## 展示图片的 HTML 示例

```html
<img src="https://images.unsplash.com/photo-xxx" alt="Beautiful sunset over mountains" width="300">
<p>📸 作者: <a href="https://unsplash.com/@photographer">Photographer Name</a> | 
<a href="https://unsplash.com/photos/xxx">查看原图</a></p>
```
