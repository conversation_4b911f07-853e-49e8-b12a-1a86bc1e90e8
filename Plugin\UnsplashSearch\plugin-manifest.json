{"manifestVersion": "1.0.0", "name": "UnsplashSearch", "displayName": "Unsplash 高质量图片搜索", "version": "1.0.0", "description": "通过 Unsplash API 搜索和获取高质量免费图片，支持关键词搜索、分类筛选和图片下载。", "author": "VCP Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node UnsplashSearch.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"UNSPLASH_ACCESS_KEY": "string", "DOWNLOAD_IMAGES": "boolean", "IMAGE_SAVE_PATH": "string"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "SearchImages", "description": "调用此工具通过 Unsplash API 搜索高质量免费图片。请在您的回复中，使用以下精确格式来请求图片搜索，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」UnsplashSearch「末」,\nquery:「始」(必需) 搜索关键词，支持英文和中文，例如：\"mountain landscape\"、\"城市夜景\"、\"cute cat\"等。「末」,\nper_page:「始」(可选, 默认10) 返回图片数量，范围1-30。「末」,\norientation:「始」(可选) 图片方向，可选值：\"landscape\"(横向)、\"portrait\"(纵向)、\"squarish\"(方形)。「末」,\ncategory:「始」(可选) 图片分类，可选值：\"backgrounds\"、\"fashion\"、\"nature\"、\"science\"、\"education\"、\"feelings\"、\"health\"、\"people\"、\"religion\"、\"places\"、\"animals\"、\"industry\"、\"computer\"、\"food\"、\"sports\"、\"transportation\"、\"travel\"、\"buildings\"、\"business\"、\"music\"。「末」,\ncolor:「始」(可选) 主色调筛选，可选值：\"black_and_white\"、\"black\"、\"white\"、\"yellow\"、\"orange\"、\"red\"、\"purple\"、\"magenta\"、\"green\"、\"teal\"、\"blue\"。「末」,\ndownload:「始」(可选, 默认false) 是否下载图片到本地，设为true时会下载高质量原图。「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含以下信息的结果：\n1. 搜索到的图片列表，包含图片URL、描述、作者信息等。\n2. 如果启用下载，还会包含本地保存路径。\n3. 图片的详细信息如尺寸、颜色等。\n请在您的最终回复中，使用返回的图片URL为用户生成HTML的 `<img>` 标签来直接展示图片，例如：`<img src=\"[图片URL]\" alt=\"[图片描述]\" width=\"300\">`。建议展示2-4张最相关的图片，并附上作者信息和Unsplash链接。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」UnsplashSearch「末」,\nquery:「始」sunset mountain landscape「末」,\nper_page:「始」5「末」,\norientation:「始」landscape「末」,\ncategory:「始」nature「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}