const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 验证Twitter Bearer Token
async function verifyToken() {
    try {
        // 读取配置
        const configPath = path.join(__dirname, 'config.env');
        let bearerToken = null;
        
        if (fs.existsSync(configPath)) {
            const config = fs.readFileSync(configPath, 'utf8');
            const lines = config.split('\n');
            for (const line of lines) {
                if (line.startsWith('TWITTER_BEARER_TOKEN=')) {
                    bearerToken = line.split('=')[1].trim().replace(/['"]/g, '');
                    break;
                }
            }
        }
        
        if (!bearerToken) {
            console.log('❌ 未找到Bearer Token');
            return;
        }
        
        console.log('🔑 Bearer Token已找到，长度:', bearerToken.length);
        console.log('🔑 Token前缀:', bearerToken.substring(0, 20) + '...');
        
        // 测试API连接
        console.log('🌐 测试Twitter API连接...');
        
        const response = await axios.get('https://api.twitter.com/2/tweets/search/recent', {
            headers: {
                'Authorization': `Bearer ${bearerToken}`,
                'Content-Type': 'application/json'
            },
            params: {
                query: 'hello',
                max_results: 10
            },
            timeout: 10000 // 10秒超时
        });
        
        console.log('✅ API连接成功！');
        console.log('📊 响应状态:', response.status);
        console.log('📊 找到推文数量:', response.data.data?.length || 0);
        
        if (response.data.data && response.data.data.length > 0) {
            console.log('📝 第一条推文预览:', response.data.data[0].text.substring(0, 50) + '...');
        }
        
    } catch (error) {
        console.log('❌ 测试失败:');
        
        if (error.response) {
            console.log('   HTTP状态:', error.response.status);
            console.log('   错误信息:', JSON.stringify(error.response.data, null, 2));
            
            if (error.response.status === 401) {
                console.log('💡 建议: Bearer Token可能无效，请检查配置');
            } else if (error.response.status === 429) {
                console.log('💡 建议: API调用频率过高，请稍后重试');
            }
        } else if (error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET') {
            console.log('   错误类型: 网络连接超时');
            console.log('💡 建议: 检查网络连接或防火墙设置');
        } else {
            console.log('   错误详情:', error.message);
        }
    }
}

console.log('🧪 Twitter API Token验证工具');
console.log('================================');
verifyToken();
