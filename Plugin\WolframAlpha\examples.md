# Wolfram Alpha Plugin 使用示例

## 数学计算示例

### 1. 基础算术

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」<PERSON><PERSON><PERSON><PERSON><PERSON>「末」,
query:「始」2 + 2「末」
<<<[END_TOOL_REQUEST]>>>
```

### 2. 方程求解

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」solve x^2 + 5x + 6 = 0「末」
<<<[END_TOOL_REQUEST]>>>
```

### 3. 复杂方程组

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」solve {x + y = 5, 2x - y = 1}「末」
<<<[END_TOOL_REQUEST]>>>
```

### 4. 不等式求解

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」<PERSON>ram<PERSON><PERSON>pha「末」,
query:「始」solve x^2 - 4 > 0「末」
<<<[END_TOOL_REQUEST]>>>
```

## 微积分示例

### 5. 导数计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」derivative of sin(x)*cos(x)「末」
<<<[END_TOOL_REQUEST]>>>
```

### 6. 高阶导数

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」second derivative of x^4 + 3x^2 + 1「末」
<<<[END_TOOL_REQUEST]>>>
```

### 7. 定积分

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」integrate x^2 from 0 to 5「末」
<<<[END_TOOL_REQUEST]>>>
```

### 8. 不定积分

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」integrate sin(x)*cos(x)「末」
<<<[END_TOOL_REQUEST]>>>
```

### 9. 多重积分

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」integrate x*y from x=0 to 1, y=0 to 2「末」
<<<[END_TOOL_REQUEST]>>>
```

## 统计和概率示例

### 10. 基础统计

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」mean of {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}「末」
<<<[END_TOOL_REQUEST]>>>
```

### 11. 标准差

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」standard deviation of {2, 4, 4, 4, 5, 5, 7, 9}「末」
<<<[END_TOOL_REQUEST]>>>
```

### 12. 正态分布

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」normal distribution mean=0 std=1「末」
<<<[END_TOOL_REQUEST]>>>
```

### 13. 概率计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」probability of getting heads 3 times in 5 coin flips「末」
<<<[END_TOOL_REQUEST]>>>
```

## 函数绘图示例

### 14. 基础函数图像

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」plot sin(x) from -pi to pi「末」,
format:「始」plaintext,image「末」,
save_image:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

### 15. 多函数对比

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」plot sin(x), cos(x), tan(x) from -pi to pi「末」,
format:「始」image「末」
<<<[END_TOOL_REQUEST]>>>
```

### 16. 3D 图像

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」plot x^2 + y^2「末」,
format:「始」image「末」
<<<[END_TOOL_REQUEST]>>>
```

### 17. 参数方程

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」parametric plot {cos(t), sin(t)} from t=0 to 2pi「末」
<<<[END_TOOL_REQUEST]>>>
```

## 单位转换示例

### 18. 温度转换

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」convert 100 fahrenheit to celsius「末」
<<<[END_TOOL_REQUEST]>>>
```

### 19. 长度转换

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」convert 5 feet to meters「末」
<<<[END_TOOL_REQUEST]>>>
```

### 20. 重量转换

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」convert 10 pounds to kilograms「末」
<<<[END_TOOL_REQUEST]>>>
```

### 21. 速度转换

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」convert 60 mph to km/h「末」
<<<[END_TOOL_REQUEST]>>>
```

## 科学查询示例

### 22. 物理常数

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」speed of light in vacuum「末」
<<<[END_TOOL_REQUEST]>>>
```

### 23. 化学元素

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」atomic mass of carbon「末」
<<<[END_TOOL_REQUEST]>>>
```

### 24. 天文数据

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」distance from earth to moon「末」
<<<[END_TOOL_REQUEST]>>>
```

### 25. 生物信息

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」human genome size「末」
<<<[END_TOOL_REQUEST]>>>
```

## 知识问答示例

### 26. 地理信息

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」population of China 2024「末」
<<<[END_TOOL_REQUEST]>>>
```

### 27. 历史事件

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」when was the first moon landing「末」
<<<[END_TOOL_REQUEST]>>>
```

### 28. 经济数据

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」GDP of United States 2023「末」
<<<[END_TOOL_REQUEST]>>>
```

### 29. 体育统计

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」world record 100m sprint「末」
<<<[END_TOOL_REQUEST]>>>
```

## 金融计算示例

### 30. 复利计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」compound interest 1000 dollars at 5% for 10 years「末」
<<<[END_TOOL_REQUEST]>>>
```

### 31. 贷款计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」monthly payment for 200000 loan at 4% for 30 years「末」
<<<[END_TOOL_REQUEST]>>>
```

### 32. 投资回报

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」return on investment 10000 to 15000「末」
<<<[END_TOOL_REQUEST]>>>
```

## 工程计算示例

### 33. 电路分析

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」voltage across 100 ohm resistor with 2 amp current「末」
<<<[END_TOOL_REQUEST]>>>
```

### 34. 力学计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」force needed to accelerate 10 kg at 5 m/s^2「末」
<<<[END_TOOL_REQUEST]>>>
```

### 35. 流体力学

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」flow rate through 2 inch pipe at 10 psi「末」
<<<[END_TOOL_REQUEST]>>>
```

## 高级数学示例

### 36. 矩阵运算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」determinant of {{1,2},{3,4}}「末」
<<<[END_TOOL_REQUEST]>>>
```

### 37. 复数计算

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」(3+4i) * (1-2i)「末」
<<<[END_TOOL_REQUEST]>>>
```

### 38. 级数求和

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」sum of 1/n^2 from n=1 to infinity「末」
<<<[END_TOOL_REQUEST]>>>
```

### 39. 傅里叶变换

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」fourier transform of exp(-x^2)「末」
<<<[END_TOOL_REQUEST]>>>
```

### 40. 微分方程

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」solve y'' + y = 0「末」
<<<[END_TOOL_REQUEST]>>>
```

## AI 使用建议

当 AI 使用这个插件时，建议：

1. **明确查询目标** - 使用清晰、具体的问题表述
2. **选择合适格式** - 数学计算用 plaintext，图表用 image
3. **利用自然语言** - Wolfram Alpha 支持自然语言查询
4. **分步骤查询** - 复杂问题可以分解为多个简单查询
5. **验证结果** - 对重要计算结果进行验证
6. **保存重要图表** - 启用图像保存功能保存有价值的图表
7. **注意查询限制** - 合理使用免费额度

## 展示结果的建议

```html
<!-- 数学结果展示 -->
<div class="wolfram-result">
  <h3>计算结果</h3>
  <p><strong>查询:</strong> solve x^2 + 5x + 6 = 0</p>
  <p><strong>解:</strong> x = -2 或 x = -3</p>
</div>

<!-- 图表展示 -->
<div class="wolfram-chart">
  <h3>函数图像</h3>
  <img src="image/wolfram/wolfram_123456789_0.png" alt="sin(x) 函数图像" width="400">
  <p><em>图表由 Wolfram Alpha 生成</em></p>
</div>
```
