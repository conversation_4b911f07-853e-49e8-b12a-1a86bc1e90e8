{"manifestVersion": "1.0.0", "name": "WolframAl<PERSON>", "displayName": "Wolfram Alpha 计算知识引擎", "version": "1.0.0", "description": "集成 Wolfram Alpha API 的强大计算知识引擎，支持数学计算、科学查询、数据分析、单位转换等复杂问题求解。", "author": "VCP Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node WolframAlpha.js"}, "communication": {"protocol": "stdio", "timeout": 30000}, "configSchema": {"WOLFRAM_APP_ID": {"type": "string", "required": true, "description": "Wolfram Alpha API App ID"}, "WOLFRAM_OUTPUT_FORMAT": {"type": "string", "required": false, "default": "plaintext,image", "description": "输出格式，可选: plaintext, image, mathml, sound"}, "WOLFRAM_UNITS": {"type": "string", "required": false, "default": "metric", "description": "单位系统: metric 或 imperial"}, "WOLFRAM_TIMEOUT": {"type": "number", "required": false, "default": 20, "description": "API 请求超时时间（秒）"}, "SAVE_IMAGES": {"type": "boolean", "required": false, "default": true, "description": "是否保存图表和图像到本地"}, "IMAGE_SAVE_PATH": {"type": "string", "required": false, "default": "image/wolfram", "description": "图像保存路径"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "调用 Wolfram Alpha 进行复杂计算、科学查询和知识问答。支持数学计算、物理公式、化学反应、统计分析、单位转换、历史事件、地理信息等。请使用以下格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WolframAlpha「末」,\nquery:「始」(必需) 要查询的问题或计算表达式，支持自然语言和数学表达式。例如：\"solve x^2 + 2x - 3 = 0\"、\"population of China\"、\"derivative of sin(x)*cos(x)\"、\"convert 100 fahrenheit to celsius\"等。「末」,\nformat:「始」(可选, 默认\"plaintext,image\") 输出格式，可选值：\"plaintext\"(纯文本)、\"image\"(图像)、\"mathml\"(数学标记)、\"sound\"(音频)，可组合使用如\"plaintext,image\"。「末」,\nunits:「始」(可选, 默认\"metric\") 单位系统，\"metric\"(公制) 或 \"imperial\"(英制)。「末」,\nsave_image:「始」(可选, 默认true) 是否保存生成的图表和图像到本地。「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含以下信息的结果：\n1. 查询的详细解答和计算结果\n2. 如果有图表或图像，会包含图像URL和本地路径\n3. 相关的数学公式、图表、步骤解释等\n4. 可能的相关查询建议\n请在回复中展示文本结果，如果有图像则使用 `<img>` 标签展示，并解释计算过程和结果的含义。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」WolframAlpha「末」,\nquery:「始」solve x^2 + 5x + 6 = 0「末」,\nformat:「始」plaintext,image「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}, "dependencies": {"node": ">=14.0.0"}, "permissions": ["network_access", "file_write", "file_read"]}