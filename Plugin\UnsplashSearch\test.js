const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 加载配置文件
function loadConfig() {
    const configPath = path.join(__dirname, 'config.env');
    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const lines = configContent.split('\n');

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, value] = trimmedLine.split('=');
                if (key && value) {
                    process.env[key.trim()] = value.trim();
                }
            }
        }
    }
}

// 在开始测试前加载配置
loadConfig();

// 测试用例
const testCases = [
    {
        name: "基础搜索测试",
        input: {
            query: "sunset mountain",
            per_page: 3
        }
    },
    {
        name: "分类筛选测试", 
        input: {
            query: "nature landscape",
            per_page: 2,
            orientation: "landscape",
            category: "nature"
        }
    },
    {
        name: "颜色筛选测试",
        input: {
            query: "ocean",
            per_page: 2,
            color: "blue",
            orientation: "landscape"
        }
    }
];

async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 运行测试: ${testCase.name}`);
        console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));
        
        const child = spawn('node', ['UnsplashSearch.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            error += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output);
                    console.log(`✅ 测试通过`);
                    console.log(`📊 结果摘要:`);
                    
                    if (result.status === 'success') {
                        console.log(`   - 状态: ${result.status}`);
                        console.log(`   - 消息: ${result.result.message}`);
                        console.log(`   - 图片数量: ${result.result.images.length}`);
                        
                        if (result.result.images.length > 0) {
                            const firstImage = result.result.images[0];
                            console.log(`   - 第一张图片:`);
                            console.log(`     * 描述: ${firstImage.description}`);
                            console.log(`     * 作者: ${firstImage.author.name}`);
                            console.log(`     * 尺寸: ${firstImage.width}x${firstImage.height}`);
                            console.log(`     * URL: ${firstImage.urls.small}`);
                        }
                    } else {
                        console.log(`   - 错误: ${result.error}`);
                    }
                    
                    resolve(result);
                } catch (parseError) {
                    console.log(`❌ JSON解析失败:`, parseError.message);
                    console.log(`原始输出:`, output);
                    reject(parseError);
                }
            } else {
                console.log(`❌ 测试失败，退出码: ${code}`);
                if (error) console.log(`错误信息: ${error}`);
                if (output) console.log(`输出: ${output}`);
                reject(new Error(`Process exited with code ${code}`));
            }
        });

        // 发送测试数据
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

async function runAllTests() {
    console.log('🚀 开始 Unsplash Search 插件测试');
    console.log('=' * 50);

    // 检查环境变量
    if (!process.env.UNSPLASH_ACCESS_KEY) {
        console.log('⚠️  警告: 未设置 UNSPLASH_ACCESS_KEY 环境变量');
        console.log('请先配置 config.env 文件或设置环境变量');
        return;
    }

    let passedTests = 0;
    let totalTests = testCases.length;

    for (const testCase of testCases) {
        try {
            await runTest(testCase);
            passedTests++;
        } catch (error) {
            console.log(`❌ 测试失败: ${error.message}`);
        }
        
        // 添加延迟避免API限制
        if (testCase !== testCases[testCases.length - 1]) {
            console.log('⏳ 等待2秒避免API限制...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    console.log('\n' + '=' * 50);
    console.log(`📈 测试总结: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！插件工作正常。');
    } else {
        console.log('⚠️  部分测试失败，请检查配置和网络连接。');
    }
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runTest, runAllTests };
