const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setupPlugin() {
    console.log('🎨 Unsplash Search Plugin 安装向导');
    console.log('=' * 50);
    
    // 检查是否已有配置文件
    const configPath = path.join(__dirname, 'config.env');
    const exampleConfigPath = path.join(__dirname, 'config.env.example');
    
    if (fs.existsSync(configPath)) {
        console.log('⚠️  检测到已存在的配置文件');
        const overwrite = await question('是否要重新配置？(y/N): ');
        if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
            console.log('✅ 保持现有配置');
            rl.close();
            return;
        }
    }
    
    console.log('\n📋 请按照以下步骤配置插件：');
    console.log('1. 访问 https://unsplash.com/developers');
    console.log('2. 注册开发者账号（如果还没有）');
    console.log('3. 创建新应用');
    console.log('4. 获取 Access Key');
    console.log('');
    
    // 获取 API Key
    const accessKey = await question('请输入您的 Unsplash Access Key: ');
    if (!accessKey.trim()) {
        console.log('❌ Access Key 不能为空');
        rl.close();
        return;
    }
    
    // 询问是否下载图片
    const downloadImages = await question('是否启用图片下载功能？(y/N): ');
    const shouldDownload = downloadImages.toLowerCase() === 'y' || downloadImages.toLowerCase() === 'yes';
    
    // 设置保存路径
    let imagePath = 'image/unsplash';
    if (shouldDownload) {
        const customPath = await question(`图片保存路径 (默认: ${imagePath}): `);
        if (customPath.trim()) {
            imagePath = customPath.trim();
        }
    }
    
    // 生成配置文件内容
    const configContent = `# Unsplash API 配置
# 获取 Access Key: https://unsplash.com/developers
UNSPLASH_ACCESS_KEY=${accessKey}

# 是否下载图片到本地 (true/false)
DOWNLOAD_IMAGES=${shouldDownload}

# 图片保存路径 (相对于项目根目录)
IMAGE_SAVE_PATH=${imagePath}
`;

    try {
        // 写入配置文件
        fs.writeFileSync(configPath, configContent, 'utf8');
        console.log('\n✅ 配置文件已创建: config.env');
        
        // 创建图片保存目录
        if (shouldDownload) {
            const fullImagePath = path.resolve(process.cwd(), '../../', imagePath);
            if (!fs.existsSync(fullImagePath)) {
                fs.mkdirSync(fullImagePath, { recursive: true });
                console.log(`✅ 图片保存目录已创建: ${fullImagePath}`);
            }
        }
        
        console.log('\n🎉 插件安装完成！');
        console.log('\n📖 使用说明：');
        console.log('1. 重启 VCP 服务器以加载插件');
        console.log('2. 在系统提示词中添加: {{VCPUnsplashSearch}}');
        console.log('3. AI 现在可以搜索 Unsplash 图片了！');
        
        console.log('\n🧪 测试插件：');
        console.log('运行: node test.js');
        
        console.log('\n📚 更多信息请查看 README.md');
        
    } catch (error) {
        console.log(`❌ 创建配置文件失败: ${error.message}`);
    }
    
    rl.close();
}

// 运行安装向导
if (require.main === module) {
    setupPlugin().catch(console.error);
}

module.exports = { setupPlugin };
