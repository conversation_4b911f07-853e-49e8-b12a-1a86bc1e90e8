{"name": "vcp-wolfram-alpha", "version": "1.0.0", "description": "Wolfram Alpha API integration plugin for VCPToolBox", "main": "WolframAlpha.js", "scripts": {"test": "node test.js", "install": "node install.js"}, "keywords": ["vcp", "wolfram", "alpha", "mathematics", "calculation", "science", "knowledge", "api"], "author": "VCP Team", "license": "CC-BY-NC-SA-4.0", "engines": {"node": ">=14.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/lioensky/VCPToolBox.git", "directory": "Plugin/WolframAlpha"}, "bugs": {"url": "https://github.com/lioensky/VCPToolBox/issues"}, "homepage": "https://github.com/lioensky/VCPToolBox#readme"}