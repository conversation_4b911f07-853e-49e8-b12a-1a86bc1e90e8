# Twitter API 测试脚本
Write-Host "🧪 Twitter API 连接测试" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

# 读取Bearer Token
$configPath = "config.env"
$bearerToken = $null

if (Test-Path $configPath) {
    $content = Get-Content $configPath
    foreach ($line in $content) {
        if ($line -match "^TWITTER_BEARER_TOKEN=(.+)$") {
            $bearerToken = $matches[1]
            break
        }
    }
}

if (-not $bearerToken) {
    Write-Host "❌ 未找到Bearer Token" -ForegroundColor Red
    exit 1
}

Write-Host "🔑 Bearer Token已找到，长度: $($bearerToken.Length)" -ForegroundColor Green
Write-Host "🌐 测试Twitter API连接..." -ForegroundColor Yellow

# 设置请求头
$headers = @{
    'Authorization' = "Bearer $bearerToken"
    'Content-Type' = 'application/json'
}

# 测试API
try {
    $uri = "https://api.twitter.com/2/tweets/search/recent?query=hello&max_results=10"
    $response = Invoke-RestMethod -Uri $uri -Headers $headers -TimeoutSec 30
    
    Write-Host "✅ API连接成功！" -ForegroundColor Green
    Write-Host "📊 响应状态: 200" -ForegroundColor Green
    
    if ($response.data) {
        Write-Host "📊 找到推文数量: $($response.data.Count)" -ForegroundColor Green
        Write-Host "📝 第一条推文预览: $($response.data[0].text.Substring(0, [Math]::Min(50, $response.data[0].text.Length)))..." -ForegroundColor Green
    } else {
        Write-Host "📊 未找到推文数据" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ 测试失败:" -ForegroundColor Red
    Write-Host "   错误信息: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "   HTTP状态: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n测试完成" -ForegroundColor Cyan
