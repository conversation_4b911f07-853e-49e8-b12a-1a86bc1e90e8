# TwitterSearch 快速开始指南

## 🚀 5分钟快速上手

### 步骤1: 获取Twitter API密钥

1. 访问 [Twitter Developer Portal](https://developer.twitter.com/)
2. 使用您的Twitter账户登录
3. 点击 "Apply for a developer account"
4. 填写申请表单（通常几分钟内批准）
5. 创建新的App项目
6. 在 "Keys and tokens" 页面生成 Bearer Token

### 步骤2: 配置插件

```bash
# 进入插件目录
cd Plugin/TwitterSearch

# 安装依赖
node install.js

# 编辑配置文件
cp config.env.example config.env
# 在config.env中设置您的Bearer Token
```

### 步骤3: 测试插件

```bash
# 运行测试
npm test
```

### 步骤4: 在VCP中使用

重启VCP服务器，然后在AI对话中使用：

```
请帮我搜索最新的AI相关推文

<<<[TOOL_REQUEST]>>>
tool_name:「始」TwitterSearch「末」,
command:「始」search_tweets「末」,
query:「始」#AI OR "artificial intelligence"「末」,
max_results:「始」20「末」
<<<[END_TOOL_REQUEST]>>>
```

## 📖 常用搜索示例

### 搜索热门话题
```
query: "#ChatGPT OR #OpenAI"
query: "#Bitcoin OR #Cryptocurrency" 
query: "#ClimateChange"
```

### 搜索特定用户
```
query: "from:elonmusk"
query: "to:openai"
query: "@username"
```

### 高级搜索
```
query: "AI -crypto"  # 包含AI但不包含crypto
query: "machine learning" AND "python"
query: "OpenAI" min_retweets:100
```

### 语言筛选
```
query: "人工智能 lang:zh"  # 中文推文
query: "AI lang:en"        # 英文推文
```

## 🔧 配置选项

### 基础配置
```env
# 必需配置
TWITTER_BEARER_TOKEN=your_bearer_token_here
```

### 高级配置（可选）
```javascript
// 在TwitterSearch.js中可以自定义
{
  "max_results": 100,        // 最大结果数
  "tweet_fields": "author_id,created_at,public_metrics",
  "user_fields": "username,name,verified,public_metrics"
}
```

## 📊 API限制说明

### 免费版本限制
- **月度推文**: 500,000条/月
- **搜索时间**: 最近7天
- **速率限制**: 300请求/15分钟窗口
- **用户查询**: 300请求/15分钟

### 付费版本优势
- 历史推文搜索（完整存档）
- 更高的速率限制
- 更多数据字段
- 实时流式API

## 🛠️ 故障排除

### 常见错误

#### 1. "Unauthorized" 错误
```
原因: Bearer Token无效或未设置
解决: 检查config.env中的TWITTER_BEARER_TOKEN
```

#### 2. "Rate limit exceeded" 错误
```
原因: 超出速率限制
解决: 等待15分钟后重试，或减少请求频率
```

#### 3. "User not found" 错误
```
原因: 用户名不存在或账户被暂停
解决: 检查用户名拼写，确认账户状态
```

#### 4. "No tweets found" 
```
原因: 搜索条件过于严格或时间范围内无匹配推文
解决: 调整搜索关键词或扩大时间范围
```

### 调试技巧

1. **启用详细日志**
```javascript
// 在TwitterSearch.js中添加
console.error('Debug:', JSON.stringify(params, null, 2));
```

2. **测试API连接**
```bash
# 运行单独测试
node test.js
```

3. **检查网络连接**
```bash
# 测试Twitter API连通性
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "https://api.twitter.com/2/tweets/search/recent?query=hello"
```

## 🎯 最佳实践

### 1. 搜索优化
- 使用具体的关键词而非泛泛的词汇
- 合理使用布尔运算符
- 设置合适的时间范围

### 2. 速率限制管理
- 批量处理请求
- 实现请求队列
- 监控API使用量

### 3. 数据处理
- 过滤垃圾内容
- 去重处理
- 数据本地缓存

### 4. 安全考虑
- 定期轮换API密钥
- 不在日志中记录敏感信息
- 遵守Twitter使用条款

## 📈 使用场景

### 1. 舆情监控
```javascript
// 监控品牌提及
query: "your_brand_name OR @your_brand"

// 竞品分析
query: "competitor_name"
```

### 2. 趋势分析
```javascript
// 热门话题
query: "#trending_topic"

// 事件追踪
query: "event_name" start_time: "2024-01-01T00:00:00Z"
```

### 3. 用户研究
```javascript
// 影响者分析
command: "get_user_info"
username: "influencer_username"

// 内容分析
command: "get_user_tweets"
exclude: "retweets,replies"
```

## 🔗 相关资源

- [Twitter API v2 文档](https://developer.twitter.com/en/docs/twitter-api)
- [搜索语法指南](https://developer.twitter.com/en/docs/twitter-api/tweets/search/integrate/build-a-query)
- [速率限制说明](https://developer.twitter.com/en/docs/twitter-api/rate-limits)
- [VCP插件开发指南](../../同步插件开发手册.md)

## 💬 支持与反馈

如果您遇到问题或有改进建议，请：
1. 查看本文档的故障排除部分
2. 检查Twitter API状态页面
3. 在VCP项目中提交Issue
4. 参考Twitter开发者社区

---

**祝您使用愉快！** 🎉
