const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始安装TwitterSearch插件...');

try {
    // 检查Node.js版本
    const nodeVersion = process.version;
    console.log(`📦 Node.js版本: ${nodeVersion}`);
    
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 14) {
        console.log('❌ 错误: 需要Node.js 14.0.0或更高版本');
        process.exit(1);
    }
    
    // 安装依赖
    console.log('📥 安装依赖包...');
    execSync('npm install', { 
        stdio: 'inherit',
        cwd: __dirname 
    });
    
    // 检查配置文件
    const configPath = path.join(__dirname, 'config.env');
    const exampleConfigPath = path.join(__dirname, 'config.env.example');
    
    if (!fs.existsSync(configPath) && fs.existsSync(exampleConfigPath)) {
        console.log('📝 创建配置文件...');
        fs.copyFileSync(exampleConfigPath, configPath);
        console.log('✅ 已创建config.env文件');
        console.log('⚠️ 请编辑config.env文件，设置您的Twitter Bearer Token');
    }
    
    // 验证插件清单
    const manifestPath = path.join(__dirname, 'plugin-manifest.json');
    if (fs.existsSync(manifestPath)) {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        console.log(`✅ 插件清单验证通过: ${manifest.displayName} v${manifest.version}`);
    }
    
    console.log('\n🎉 TwitterSearch插件安装完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. 访问 https://developer.twitter.com/ 申请开发者账户');
    console.log('2. 创建新的App并生成Bearer Token');
    console.log('3. 编辑config.env文件，设置TWITTER_BEARER_TOKEN');
    console.log('4. 重启VCP服务器以加载插件');
    console.log('5. 运行 npm test 测试插件功能');
    
    console.log('\n💡 使用提示:');
    console.log('- 免费版本限制: 500,000推文/月');
    console.log('- 搜索范围: 最近7天的推文');
    console.log('- 速率限制: 300请求/15分钟');
    
} catch (error) {
    console.log('❌ 安装失败:', error.message);
    process.exit(1);
}
