const https = require('https');
const fs = require('fs');
const path = require('path');
const { URL } = require('url');

// 加载配置文件
function loadConfigFile() {
    const configPath = path.join(__dirname, 'config.env');
    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const lines = configContent.split('\n');
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, value] = trimmedLine.split('=');
                if (key && value) {
                    process.env[key.trim()] = value.trim();
                }
            }
        }
    }
}

// 在类定义前加载配置
loadConfigFile();

class WolframAlpha {
    constructor() {
        this.config = this.loadConfig();
        this.baseURL = 'https://api.wolframalpha.com/v2/query';
    }

    loadConfig() {
        const config = {};
        
        // 从环境变量加载配置
        config.appId = process.env.WOLFRAM_APP_ID;
        config.outputFormat = process.env.WOLFRAM_OUTPUT_FORMAT || 'plaintext,image';
        config.units = process.env.WOLFRAM_UNITS || 'metric';
        config.timeout = parseInt(process.env.WOLFRAM_TIMEOUT) || 20;
        config.saveImages = process.env.SAVE_IMAGES !== 'false';
        config.imageSavePath = process.env.IMAGE_SAVE_PATH || 'image/wolfram';
        
        if (!config.appId) {
            throw new Error('WOLFRAM_APP_ID is required. Get it from https://products.wolframalpha.com/api/');
        }
        
        return config;
    }

    async makeRequest(params) {
        return new Promise((resolve, reject) => {
            const url = new URL(this.baseURL);
            
            // 添加基础参数
            const queryParams = {
                appid: this.config.appId,
                format: this.config.outputFormat,
                units: this.config.units,
                ...params
            };

            // 添加查询参数
            Object.keys(queryParams).forEach(key => {
                if (queryParams[key] !== undefined && queryParams[key] !== null) {
                    url.searchParams.append(key, queryParams[key]);
                }
            });

            const options = {
                hostname: url.hostname,
                path: url.pathname + url.search,
                method: 'GET',
                headers: {
                    'User-Agent': 'VCP-WolframAlpha/1.0.0'
                },
                timeout: this.config.timeout * 1000
            };

            const req = https.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(data);
                    } else {
                        reject(new Error(`API Error: ${res.statusCode} - ${data}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`Request failed: ${error.message}`));
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            req.end();
        });
    }

    async downloadImage(imageUrl, filename) {
        return new Promise((resolve, reject) => {
            // 确保保存目录存在
            const saveDir = path.resolve(this.config.imageSavePath);
            if (!fs.existsSync(saveDir)) {
                fs.mkdirSync(saveDir, { recursive: true });
            }

            const filePath = path.join(saveDir, filename);
            const file = fs.createWriteStream(filePath);

            https.get(imageUrl, (response) => {
                if (response.statusCode !== 200) {
                    reject(new Error(`Failed to download image: ${response.statusCode}`));
                    return;
                }

                response.pipe(file);

                file.on('finish', () => {
                    file.close();
                    resolve(filePath);
                });

                file.on('error', (error) => {
                    fs.unlink(filePath, () => {}); // 删除部分下载的文件
                    reject(error);
                });
            }).on('error', (error) => {
                reject(error);
            });
        });
    }

    parseXMLResponse(xmlData) {
        try {
            // 简单的 XML 解析，提取关键信息
            const results = {
                success: false,
                error: null,
                pods: [],
                images: [],
                assumptions: [],
                tips: [],
                warnings: []
            };

            // 检查查询是否成功
            const successMatch = xmlData.match(/success=['"]([^'"]*)['"]/);
            results.success = successMatch && successMatch[1] === 'true';

            if (!results.success) {
                const errorMatch = xmlData.match(/<error[^>]*>(.*?)<\/error>/s);
                results.error = errorMatch ? errorMatch[1].trim() : 'Unknown error';
                return results;
            }

            // 提取 pods (结果块)
            const podMatches = xmlData.match(/<pod[^>]*>.*?<\/pod>/gs);
            if (podMatches) {
                for (const podMatch of podMatches) {
                    const pod = this.parsePod(podMatch);
                    if (pod) {
                        results.pods.push(pod);
                    }
                }
            }

            // 提取图像
            const imgMatches = xmlData.match(/<img[^>]*src=['"]([^'"]*)['"]/g);
            if (imgMatches) {
                for (const imgMatch of imgMatches) {
                    const srcMatch = imgMatch.match(/src=['"]([^'"]*)['"]/);
                    if (srcMatch) {
                        results.images.push(srcMatch[1]);
                    }
                }
            }

            // 提取假设和提示
            const assumptionMatches = xmlData.match(/<assumption[^>]*>.*?<\/assumption>/gs);
            if (assumptionMatches) {
                for (const assumption of assumptionMatches) {
                    const textMatch = assumption.match(/<text[^>]*>(.*?)<\/text>/s);
                    if (textMatch) {
                        results.assumptions.push(textMatch[1].trim());
                    }
                }
            }

            return results;
        } catch (error) {
            return {
                success: false,
                error: `XML parsing error: ${error.message}`,
                pods: [],
                images: [],
                assumptions: [],
                tips: [],
                warnings: []
            };
        }
    }

    parsePod(podXML) {
        try {
            const pod = {
                title: '',
                content: [],
                images: []
            };

            // 提取标题
            const titleMatch = podXML.match(/title=['"]([^'"]*)['"]/);
            pod.title = titleMatch ? titleMatch[1] : 'Unknown';

            // 提取子pods
            const subpodMatches = podXML.match(/<subpod[^>]*>.*?<\/subpod>/gs);
            if (subpodMatches) {
                for (const subpodMatch of subpodMatches) {
                    // 提取纯文本内容
                    const plaintextMatch = subpodMatch.match(/<plaintext[^>]*>(.*?)<\/plaintext>/s);
                    if (plaintextMatch && plaintextMatch[1].trim()) {
                        pod.content.push(plaintextMatch[1].trim());
                    }

                    // 提取图像
                    const imgMatches = subpodMatch.match(/<img[^>]*src=['"]([^'"]*)['"]/g);
                    if (imgMatches) {
                        for (const imgMatch of imgMatches) {
                            const srcMatch = imgMatch.match(/src=['"]([^'"]*)['"]/);
                            if (srcMatch) {
                                pod.images.push(srcMatch[1]);
                            }
                        }
                    }
                }
            }

            return pod.content.length > 0 || pod.images.length > 0 ? pod : null;
        } catch (error) {
            return null;
        }
    }

    async query(params) {
        try {
            const queryParams = {
                input: params.query,
                format: params.format || this.config.outputFormat,
                units: params.units || this.config.units
            };

            const xmlResponse = await this.makeRequest(queryParams);
            const parsedResult = this.parseXMLResponse(xmlResponse);

            if (!parsedResult.success) {
                return {
                    status: 'error',
                    error: parsedResult.error || 'Query failed'
                };
            }

            const result = {
                query: params.query,
                success: true,
                pods: [],
                images: [],
                summary: ''
            };

            // 处理结果pods
            for (const pod of parsedResult.pods) {
                const processedPod = {
                    title: pod.title,
                    content: pod.content,
                    images: []
                };

                // 处理图像
                if (pod.images.length > 0 && params.save_image !== false && this.config.saveImages) {
                    for (let i = 0; i < pod.images.length; i++) {
                        try {
                            const imageUrl = pod.images[i];
                            const timestamp = Date.now();
                            const filename = `wolfram_${timestamp}_${i}.png`;
                            const localPath = await this.downloadImage(imageUrl, filename);

                            processedPod.images.push({
                                url: imageUrl,
                                local_path: localPath,
                                filename: filename
                            });
                        } catch (downloadError) {
                            processedPod.images.push({
                                url: pod.images[i],
                                download_error: downloadError.message
                            });
                        }
                    }
                } else {
                    // 只保存URL，不下载
                    processedPod.images = pod.images.map(url => ({ url }));
                }

                result.pods.push(processedPod);
            }

            // 生成摘要
            if (result.pods.length > 0) {
                const inputPod = result.pods.find(p => p.title.toLowerCase().includes('input'));
                const resultPod = result.pods.find(p =>
                    p.title.toLowerCase().includes('result') ||
                    p.title.toLowerCase().includes('solution') ||
                    p.title.toLowerCase().includes('answer')
                );

                if (resultPod && resultPod.content.length > 0) {
                    result.summary = `查询: ${params.query}\n结果: ${resultPod.content[0]}`;
                } else if (result.pods[0] && result.pods[0].content.length > 0) {
                    result.summary = `查询: ${params.query}\n信息: ${result.pods[0].content[0]}`;
                }
            }

            return {
                status: 'success',
                result: result
            };

        } catch (error) {
            return {
                status: 'error',
                error: `Wolfram Alpha query failed: ${error.message}`
            };
        }
    }
}

// 主函数
async function main() {
    try {
        // 从标准输入读取参数
        let inputData = '';
        process.stdin.setEncoding('utf8');

        for await (const chunk of process.stdin) {
            inputData += chunk;
        }

        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());

        if (!params.query) {
            throw new Error('query parameter is required');
        }

        const wolfram = new WolframAlpha();
        const result = await wolfram.query(params);

        console.log(JSON.stringify(result, null, 2));

    } catch (error) {
        const errorResult = {
            status: 'error',
            error: error.message
        };
        console.log(JSON.stringify(errorResult, null, 2));
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = WolframAlpha;
