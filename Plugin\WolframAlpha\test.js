const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 加载配置文件
function loadConfig() {
    const configPath = path.join(__dirname, 'config.env');
    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const lines = configContent.split('\n');
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, value] = trimmedLine.split('=');
                if (key && value) {
                    process.env[key.trim()] = value.trim();
                }
            }
        }
    }
}

// 在开始测试前加载配置
loadConfig();

// 测试用例
const testCases = [
    {
        name: "基础数学计算",
        input: {
            query: "2 + 2"
        }
    },
    {
        name: "方程求解",
        input: {
            query: "solve x^2 + 5x + 6 = 0"
        }
    },
    {
        name: "微积分计算",
        input: {
            query: "derivative of sin(x)*cos(x)"
        }
    },
    {
        name: "积分计算",
        input: {
            query: "integrate x^2 from 0 to 5"
        }
    },
    {
        name: "单位转换",
        input: {
            query: "convert 100 fahrenheit to celsius"
        }
    },
    {
        name: "科学常数",
        input: {
            query: "speed of light"
        }
    },
    {
        name: "统计计算",
        input: {
            query: "mean of {1, 2, 3, 4, 5}"
        }
    },
    {
        name: "函数绘图",
        input: {
            query: "plot sin(x) from -pi to pi",
            format: "plaintext,image",
            save_image: true
        }
    }
];

async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 运行测试: ${testCase.name}`);
        console.log(`📝 查询内容: ${testCase.input.query}`);
        
        const child = spawn('node', ['WolframAlpha.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            error += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output);
                    console.log(`✅ 测试通过`);
                    console.log(`📊 结果摘要:`);
                    
                    if (result.status === 'success') {
                        console.log(`   - 状态: ${result.status}`);
                        console.log(`   - 查询: ${result.result.query}`);
                        console.log(`   - 成功: ${result.result.success}`);
                        
                        if (result.result.summary) {
                            console.log(`   - 摘要: ${result.result.summary}`);
                        }
                        
                        if (result.result.pods && result.result.pods.length > 0) {
                            console.log(`   - 结果块数量: ${result.result.pods.length}`);
                            
                            // 显示主要结果
                            const resultPod = result.result.pods.find(p => 
                                p.title.toLowerCase().includes('result') || 
                                p.title.toLowerCase().includes('solution') ||
                                p.title.toLowerCase().includes('answer')
                            );
                            
                            if (resultPod && resultPod.content.length > 0) {
                                console.log(`   - 主要结果: ${resultPod.content[0]}`);
                            }
                            
                            // 显示图像信息
                            const totalImages = result.result.pods.reduce((sum, pod) => sum + pod.images.length, 0);
                            if (totalImages > 0) {
                                console.log(`   - 生成图像: ${totalImages} 张`);
                                
                                // 显示第一张图像的信息
                                for (const pod of result.result.pods) {
                                    if (pod.images.length > 0) {
                                        const firstImage = pod.images[0];
                                        if (firstImage.local_path) {
                                            console.log(`   - 图像保存: ${firstImage.filename}`);
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    } else {
                        console.log(`   - 错误: ${result.error}`);
                    }
                    
                    resolve(result);
                } catch (parseError) {
                    console.log(`❌ JSON解析失败:`, parseError.message);
                    console.log(`原始输出:`, output);
                    reject(parseError);
                }
            } else {
                console.log(`❌ 测试失败，退出码: ${code}`);
                if (error) console.log(`错误信息: ${error}`);
                if (output) console.log(`输出: ${output}`);
                reject(new Error(`Process exited with code ${code}`));
            }
        });

        // 发送测试数据
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

async function runAllTests() {
    console.log('🚀 开始 Wolfram Alpha 插件测试');
    console.log('=' * 50);

    // 检查环境变量
    if (!process.env.WOLFRAM_APP_ID) {
        console.log('⚠️  警告: 未设置 WOLFRAM_APP_ID 环境变量');
        console.log('请先配置 config.env 文件或设置环境变量');
        console.log('获取 App ID: https://products.wolframalpha.com/api/');
        return;
    }

    let passedTests = 0;
    let totalTests = testCases.length;

    for (const testCase of testCases) {
        try {
            await runTest(testCase);
            passedTests++;
        } catch (error) {
            console.log(`❌ 测试失败: ${error.message}`);
        }
        
        // 添加延迟避免API限制
        if (testCase !== testCases[testCases.length - 1]) {
            console.log('⏳ 等待3秒避免API限制...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('\n' + '=' * 50);
    console.log(`📈 测试总结: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！插件工作正常。');
        console.log('\n💡 提示:');
        console.log('- 检查 image/wolfram 目录查看生成的图像');
        console.log('- Wolfram Alpha 提供了强大的计算和知识查询能力');
        console.log('- 免费账户每月有 2000 次查询限制');
    } else {
        console.log('⚠️  部分测试失败，请检查配置和网络连接。');
    }
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runTest, runAllTests };
