# Unsplash Search Plugin

这是一个用于 VCPToolBox 的 Unsplash API 集成插件，允许 AI 搜索和获取高质量的免费图片。

## 功能特性

- 🔍 **智能搜索**: 支持中英文关键词搜索
- 🎨 **多维筛选**: 支持按方向、分类、颜色等筛选
- 📥 **图片下载**: 可选择下载高质量原图到本地
- 👤 **作者信息**: 提供图片作者和版权信息
- 🏷️ **标签系统**: 自动提取图片相关标签
- ⚡ **高效API**: 每小时50次免费请求

## 配置说明

### 1. 获取 Unsplash API Key

1. 访问 [Unsplash Developers](https://unsplash.com/developers)
2. 注册开发者账号
3. 创建新应用
4. 获取 Access Key

### 2. 配置插件

复制 `config.env.example` 为 `config.env` 并填写：

```env
# Unsplash API 配置
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# 是否下载图片到本地
DOWNLOAD_IMAGES=false

# 图片保存路径
IMAGE_SAVE_PATH=image/unsplash
```

## 使用方法

### 基础搜索

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」sunset mountain landscape「末」,
per_page:「始」5「末」
<<<[END_TOOL_REQUEST]>>>
```

### 高级筛选

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」城市夜景「末」,
per_page:「始」8「末」,
orientation:「始」landscape「末」,
category:「始」places「末」,
color:「始」blue「末」
<<<[END_TOOL_REQUEST]>>>
```

### 下载图片

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」cute cat「末」,
per_page:「始」3「末」,
download:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

## 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `query` | string | ✅ | 搜索关键词，支持中英文 |
| `per_page` | number | ❌ | 返回图片数量 (1-30，默认10) |
| `orientation` | string | ❌ | 图片方向：landscape/portrait/squarish |
| `category` | string | ❌ | 图片分类：nature/people/technology等 |
| `color` | string | ❌ | 主色调：red/blue/green等 |
| `download` | boolean | ❌ | 是否下载到本地 (默认false) |

## 支持的分类

- `backgrounds` - 背景图片
- `fashion` - 时尚
- `nature` - 自然风景
- `science` - 科学技术
- `education` - 教育
- `feelings` - 情感表达
- `health` - 健康医疗
- `people` - 人物肖像
- `religion` - 宗教文化
- `places` - 地点建筑
- `animals` - 动物
- `industry` - 工业制造
- `computer` - 计算机技术
- `food` - 美食
- `sports` - 体育运动
- `transportation` - 交通工具
- `travel` - 旅行
- `buildings` - 建筑
- `business` - 商业
- `music` - 音乐

## 支持的颜色

- `black_and_white` - 黑白
- `black` - 黑色
- `white` - 白色
- `yellow` - 黄色
- `orange` - 橙色
- `red` - 红色
- `purple` - 紫色
- `magenta` - 洋红
- `green` - 绿色
- `teal` - 青色
- `blue` - 蓝色

## 返回数据格式

```json
{
  "status": "success",
  "result": {
    "message": "找到 5 张与\"sunset\"相关的高质量图片",
    "total": 1000,
    "images": [
      {
        "id": "photo_id",
        "description": "Beautiful sunset over mountains",
        "urls": {
          "regular": "https://images.unsplash.com/...",
          "small": "https://images.unsplash.com/...",
          "thumb": "https://images.unsplash.com/..."
        },
        "width": 4000,
        "height": 3000,
        "color": "#ff6b35",
        "author": {
          "name": "John Doe",
          "username": "johndoe",
          "profile": "https://unsplash.com/@johndoe"
        },
        "unsplash_url": "https://unsplash.com/photos/...",
        "tags": ["sunset", "mountain", "landscape"]
      }
    ]
  }
}
```

## 注意事项

1. **API 限制**: 免费账户每小时50次请求
2. **版权说明**: Unsplash 图片遵循 Unsplash License，可免费用于商业和非商业用途
3. **作者署名**: 建议在使用图片时署名作者
4. **存储空间**: 启用下载功能时注意本地存储空间
5. **网络要求**: 需要稳定的网络连接访问 Unsplash API

## 故障排除

### 常见错误

1. **401 Unauthorized**: 检查 Access Key 是否正确
2. **403 Rate Limit**: 超出每小时请求限制，请稍后重试
3. **网络错误**: 检查网络连接和防火墙设置

### 调试模式

可以通过查看 VCP 日志来调试插件运行状态。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础图片搜索
- 支持多维度筛选
- 支持图片下载功能
- 完整的错误处理机制
