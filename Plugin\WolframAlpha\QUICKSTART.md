# 🚀 Wolfram Alpha Plugin 快速启动指南

## 📦 安装步骤

### 1. 获取 Wolfram Alpha App ID

1. 访问 [Wolfram Alpha API](https://products.wolframalpha.com/api/)
2. 点击 "Get API Access"
3. 注册开发者账号
4. 创建新应用
5. 复制 App ID

### 2. 配置插件

运行安装向导：
```bash
cd Plugin/WolframAlpha
node install.js
```

或手动配置：
```bash
cp config.env.example config.env
# 编辑 config.env 文件，填入您的 App ID
```

### 3. 重启 VCP 服务器

```bash
# 在项目根目录
node server.js
```

### 4. 更新系统提示词

在您的 AI 系统提示词中添加：
```
{{VCPWolframAlpha}}
```

## 🧪 测试插件

```bash
cd Plugin/WolframAlpha
node test.js
```

## 🎯 快速使用

### 基础数学计算
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」solve x^2 + 5x + 6 = 0「末」
<<<[END_TOOL_REQUEST]>>>
```

### 微积分
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」derivative of sin(x)*cos(x)「末」
<<<[END_TOOL_REQUEST]>>>
```

### 单位转换
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」convert 100 fahrenheit to celsius「末」
<<<[END_TOOL_REQUEST]>>>
```

### 函数绘图
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」plot sin(x) from -pi to pi「末」,
format:「始」plaintext,image「末」,
save_image:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

### 科学查询
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」speed of light in vacuum「末」
<<<[END_TOOL_REQUEST]>>>
```

### 知识问答
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」WolframAlpha「末」,
query:「始」population of China 2024「末」
<<<[END_TOOL_REQUEST]>>>
```

## 📊 API 限制

- **免费账户**: 每月 2000 次查询
- **开发者账户**: 每月 2000 次查询
- **商业账户**: 根据订阅计划

## 🔧 常见问题

### Q: 401 Unauthorized 错误
A: 检查 App ID 是否正确配置

### Q: 403 Quota Exceeded 错误  
A: 超出每月查询限制，请等待下月重置或升级账户

### Q: 查询无结果
A: 尝试重新表述问题，使用更清晰的英文表达

### Q: 图像下载失败
A: 确保有写入权限，检查保存路径

### Q: 查询超时
A: 复杂查询可能需要更长时间，可以增加超时设置

## 💡 使用技巧

### 1. 查询语言
- **推荐**: 使用英文查询获得最佳结果
- **支持**: 自然语言和数学表达式
- **示例**: "solve equation" 比 "求解方程" 效果更好

### 2. 输出格式选择
- **plaintext**: 适合纯文本结果
- **image**: 适合图表和可视化
- **plaintext,image**: 获得完整信息（推荐）

### 3. 复杂查询技巧
- 分步骤查询复杂问题
- 使用具体的数学符号
- 明确指定变量和范围

### 4. 图像管理
- 启用图像保存获得高质量图表
- 定期清理图像文件夹
- 重要图表可以重命名保存

## 🆚 与 SciCalculator 对比

| 功能 | SciCalculator | WolframAlpha |
|------|---------------|--------------|
| **计算速度** | 快速 | 较慢（网络请求） |
| **计算范围** | 基础数学 | 广泛知识领域 |
| **离线使用** | ✅ | ❌ |
| **图表生成** | ❌ | ✅ |
| **自然语言** | ❌ | ✅ |
| **使用限制** | 无 | 每月2000次 |

## 📚 更多资源

- [完整文档](README.md)
- [使用示例](examples.md)
- [Wolfram Alpha API 文档](https://products.wolframalpha.com/api/documentation/)
- [VCPToolBox 项目](https://github.com/lioensky/VCPToolBox)

## 🆘 获取帮助

如果遇到问题：
1. 查看 VCP 服务器日志
2. 运行测试脚本诊断
3. 检查网络连接
4. 验证 App ID 有效性
5. 查看 Wolfram Alpha 官方文档

---

🎉 **恭喜！您现在拥有了世界级的计算知识引擎！**

Wolfram Alpha 将为您的 AI 助手提供：
- 🧮 强大的数学计算能力
- 🔬 丰富的科学知识库
- 📊 专业的数据可视化
- 🌍 实时的知识查询

让您的 AI 助手变得更加智能和专业！
