{"name": "vcp-unsplash-search", "version": "1.0.0", "description": "Unsplash API integration plugin for VCPToolBox", "main": "UnsplashSearch.js", "scripts": {"test": "node test.js", "install": "node install.js"}, "keywords": ["vcp", "unsplash", "image", "search", "photography", "api"], "author": "VCP Team", "license": "CC-BY-NC-SA-4.0", "engines": {"node": ">=14.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/lioensky/VCPToolBox.git", "directory": "Plugin/UnsplashSearch"}, "bugs": {"url": "https://github.com/lioensky/VCPToolBox/issues"}, "homepage": "https://github.com/lioensky/VCPToolBox#readme"}