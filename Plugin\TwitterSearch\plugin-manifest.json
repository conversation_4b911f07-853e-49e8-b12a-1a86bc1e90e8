{"name": "TwitterSearch", "displayName": "Twitter API v2 搜索", "version": "1.0.0", "description": "基于Twitter API v2的推特数据获取和搜索插件，支持推文搜索、用户信息查询和用户推文获取", "author": "VCP Team", "pluginType": "synchronous", "entryPoint": "node TwitterSearch.js", "communication": {"protocol": "stdio"}, "configSchema": {"TWITTER_BEARER_TOKEN": {"type": "string", "description": "Twitter API v2 Bearer <PERSON>，从 https://developer.twitter.com/ 获取", "required": true, "sensitive": true}}, "capabilities": {"invocationCommands": [{"command": "search_tweets", "description": "搜索推特内容\n\n参数说明：\n- query (必需): 搜索关键词或话题，支持Twitter搜索语法\n  * 基础搜索: \"AI\", \"machine learning\"\n  * 话题标签: \"#AI\", \"#MachineLearning\"\n  * 组合搜索: \"#AI OR #MachineLearning\", \"AI -crypto\"\n  * 用户搜索: \"from:elonmusk\", \"to:openai\"\n  * 时间范围: 可通过start_time和end_time参数指定\n- max_results (可选): 返回推文数量，范围10-100，默认10\n- tweet_fields (可选): 返回的推文字段，默认包含作者、创建时间、互动数据、语言等\n- user_fields (可选): 返回的用户字段，默认包含用户名、显示名、认证状态、粉丝数等\n- start_time (可选): 搜索开始时间，ISO 8601格式，如\"2024-01-01T00:00:00Z\"\n- end_time (可选): 搜索结束时间，ISO 8601格式\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TwitterSearch「末」,\ncommand:「始」search_tweets「末」,\nquery:「始」#AI OR #MachineLearning「末」,\nmax_results:「始」20「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含推文列表，每条推文包含文本内容、作者信息、互动数据、创建时间和推文链接", "example": "搜索AI相关推文：query=\"#AI OR artificial intelligence\", max_results=15"}, {"command": "get_user_info", "description": "获取Twitter用户详细信息\n\n参数说明：\n- username (必需): Twitter用户名，不包含@符号\n- user_fields (可选): 返回的用户字段，默认包含创建时间、简介、粉丝数、认证状态、位置等\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TwitterSearch「末」,\ncommand:「始」get_user_info「末」,\nusername:「始」elonmusk「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含用户ID、用户名、显示名、简介、创建时间、认证状态、位置、个人网站、粉丝数、关注数、推文数等详细信息", "example": "获取用户信息：username=\"<PERSON><PERSON>\""}, {"command": "get_user_tweets", "description": "获取指定用户的最新推文\n\n参数说明：\n- username (必需): Twitter用户名，不包含@符号\n- max_results (可选): 返回推文数量，范围5-100，默认10\n- exclude (可选): 排除的推文类型，如\"retweets,replies\"，默认排除转推\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TwitterSearch「末」,\ncommand:「始」get_user_tweets「末」,\nusername:「始」openai「末」,\nmax_results:「始」15「末」,\nexclude:「始」retweets,replies「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含用户基本信息和推文列表，每条推文包含文本内容、互动数据、创建时间和推文链接", "example": "获取用户推文：username=\"elonmusk\", max_results=20, exclude=\"retweets\""}]}, "dependencies": {"axios": "^1.6.0"}, "tags": ["social", "twitter", "search", "api", "data"], "category": "Social Media", "license": "CC BY-NC-SA 4.0"}