@echo off
echo 🧪 使用curl测试Twitter API连接
echo ================================

REM 读取Bearer Token
for /f "tokens=2 delims==" %%a in ('findstr "TWITTER_BEARER_TOKEN=" config.env') do set BEARER_TOKEN=%%a

if "%BEARER_TOKEN%"=="" (
    echo ❌ 未找到Bearer Token
    pause
    exit /b 1
)

echo 🔑 Bearer Token已找到
echo 🌐 测试Twitter API连接...

REM 测试API连接
curl -H "Authorization: Bearer %BEARER_TOKEN%" ^
     -H "Content-Type: application/json" ^
     --connect-timeout 30 ^
     --max-time 60 ^
     "https://api.twitter.com/2/tweets/search/recent?query=hello&max_results=10"

echo.
echo ================================
echo 测试完成
pause
