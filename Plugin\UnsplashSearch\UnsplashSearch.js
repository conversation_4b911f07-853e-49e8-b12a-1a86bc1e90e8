const https = require('https');
const fs = require('fs');
const path = require('path');
const { URL } = require('url');

// 加载配置文件
function loadConfigFile() {
    const configPath = path.join(__dirname, 'config.env');
    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const lines = configContent.split('\n');

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, value] = trimmedLine.split('=');
                if (key && value) {
                    process.env[key.trim()] = value.trim();
                }
            }
        }
    }
}

// 在类定义前加载配置
loadConfigFile();

class UnsplashSearch {
    constructor() {
        this.config = this.loadConfig();
        this.baseURL = 'https://api.unsplash.com';
    }

    loadConfig() {
        const config = {};
        
        // 从环境变量或配置文件加载配置
        config.accessKey = process.env.UNSPLASH_ACCESS_KEY;
        config.downloadImages = process.env.DOWNLOAD_IMAGES === 'true';
        config.imageSavePath = process.env.IMAGE_SAVE_PATH || 'image/unsplash';
        
        if (!config.accessKey) {
            throw new Error('UNSPLASH_ACCESS_KEY is required');
        }
        
        return config;
    }

    async makeRequest(endpoint, params = {}) {
        return new Promise((resolve, reject) => {
            const url = new URL(endpoint, this.baseURL);
            
            // 添加查询参数
            Object.keys(params).forEach(key => {
                if (params[key] !== undefined && params[key] !== null) {
                    url.searchParams.append(key, params[key]);
                }
            });

            const options = {
                hostname: url.hostname,
                path: url.pathname + url.search,
                method: 'GET',
                headers: {
                    'Authorization': `Client-ID ${this.config.accessKey}`,
                    'User-Agent': 'VCP-UnsplashSearch/1.0.0'
                }
            };

            const req = https.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(`API Error: ${res.statusCode} - ${jsonData.errors ? jsonData.errors.join(', ') : 'Unknown error'}`));
                        }
                    } catch (error) {
                        reject(new Error(`Failed to parse response: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`Request failed: ${error.message}`));
            });

            req.end();
        });
    }

    async downloadImage(imageUrl, filename) {
        return new Promise((resolve, reject) => {
            // 确保保存目录存在
            const saveDir = path.resolve(this.config.imageSavePath);
            if (!fs.existsSync(saveDir)) {
                fs.mkdirSync(saveDir, { recursive: true });
            }

            const filePath = path.join(saveDir, filename);
            const file = fs.createWriteStream(filePath);

            https.get(imageUrl, (response) => {
                if (response.statusCode !== 200) {
                    reject(new Error(`Failed to download image: ${response.statusCode}`));
                    return;
                }

                response.pipe(file);

                file.on('finish', () => {
                    file.close();
                    resolve(filePath);
                });

                file.on('error', (error) => {
                    fs.unlink(filePath, () => {}); // 删除部分下载的文件
                    reject(error);
                });
            }).on('error', (error) => {
                reject(error);
            });
        });
    }

    async searchImages(params) {
        try {
            const searchParams = {
                query: params.query,
                per_page: Math.min(params.per_page || 10, 30), // 限制最大30张
                orientation: params.orientation,
                category: params.category,
                color: params.color,
                order_by: 'relevant' // 按相关性排序
            };

            const response = await this.makeRequest('/search/photos', searchParams);
            
            if (!response.results || response.results.length === 0) {
                return {
                    status: 'success',
                    result: {
                        message: `未找到与"${params.query}"相关的图片`,
                        total: 0,
                        images: []
                    }
                };
            }

            const images = [];
            
            for (const photo of response.results) {
                const imageInfo = {
                    id: photo.id,
                    description: photo.description || photo.alt_description || '无描述',
                    urls: {
                        raw: photo.urls.raw,
                        full: photo.urls.full,
                        regular: photo.urls.regular,
                        small: photo.urls.small,
                        thumb: photo.urls.thumb
                    },
                    width: photo.width,
                    height: photo.height,
                    color: photo.color,
                    author: {
                        name: photo.user.name,
                        username: photo.user.username,
                        profile: `https://unsplash.com/@${photo.user.username}`
                    },
                    unsplash_url: photo.links.html,
                    download_url: photo.links.download,
                    tags: photo.tags ? photo.tags.map(tag => tag.title).slice(0, 5) : []
                };

                // 如果启用下载
                if (params.download && this.config.downloadImages) {
                    try {
                        const filename = `unsplash_${photo.id}_${Date.now()}.jpg`;
                        const localPath = await this.downloadImage(photo.urls.regular, filename);
                        imageInfo.local_path = localPath;
                        imageInfo.local_filename = filename;
                    } catch (downloadError) {
                        imageInfo.download_error = downloadError.message;
                    }
                }

                images.push(imageInfo);
            }

            return {
                status: 'success',
                result: {
                    message: `找到 ${images.length} 张与"${params.query}"相关的高质量图片`,
                    total: response.total,
                    total_pages: response.total_pages,
                    current_page: 1,
                    images: images
                }
            };

        } catch (error) {
            return {
                status: 'error',
                error: `搜索图片失败: ${error.message}`
            };
        }
    }
}

// 主函数
async function main() {
    try {
        // 从标准输入读取参数
        let inputData = '';
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }

        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());
        
        if (!params.query) {
            throw new Error('query parameter is required');
        }

        const unsplash = new UnsplashSearch();
        const result = await unsplash.searchImages(params);
        
        console.log(JSON.stringify(result, null, 2));
        
    } catch (error) {
        const errorResult = {
            status: 'error',
            error: error.message
        };
        console.log(JSON.stringify(errorResult, null, 2));
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = UnsplashSearch;
