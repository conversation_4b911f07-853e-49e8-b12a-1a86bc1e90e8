const { spawn } = require('child_process');

// 最终测试 - 测试不同的查询
async function finalTest() {
    console.log('🧪 TwitterSearch插件最终测试');
    console.log('================================');
    
    const testCases = [
        {
            name: '简单搜索测试',
            input: {
                command: "search_tweets",
                query: "hello",
                max_results: 5
            }
        },
        {
            name: '获取Twitter官方账户信息',
            input: {
                command: "get_user_info",
                username: "twitter"
            }
        }
    ];
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n📋 测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
        
        await new Promise((resolve) => {
            const child = spawn('node', ['TwitterSearch.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: __dirname
            });
            
            let output = '';
            let error = '';
            
            child.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            child.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            child.on('close', (code) => {
                console.log(`   退出代码: ${code}`);
                
                if (error) {
                    console.log(`   ⚠️ 错误输出: ${error.trim()}`);
                }
                
                if (output) {
                    try {
                        const result = JSON.parse(output.trim());
                        
                        if (result.status === 'success') {
                            console.log(`   ✅ 测试成功！`);
                            
                            if (result.result.tweets) {
                                console.log(`   📊 找到推文: ${result.result.tweets.length} 条`);
                                if (result.result.tweets.length > 0) {
                                    const tweet = result.result.tweets[0];
                                    console.log(`   📝 示例推文: ${tweet.text.substring(0, 60)}...`);
                                    console.log(`   👤 作者: @${tweet.author.username}`);
                                }
                            } else if (result.result.username) {
                                console.log(`   👤 用户: @${result.result.username} (${result.result.name})`);
                                console.log(`   👥 粉丝数: ${result.result.metrics.followers_count.toLocaleString()}`);
                                console.log(`   ✅ 认证状态: ${result.result.verified ? '已认证' : '未认证'}`);
                            }
                        } else {
                            console.log(`   ❌ 测试失败: ${result.error}`);
                            
                            // 分析错误类型
                            if (result.error.includes('timeout') || result.error.includes('ETIMEDOUT')) {
                                console.log(`   💡 建议: 网络连接超时，可能需要检查网络设置`);
                            } else if (result.error.includes('401')) {
                                console.log(`   💡 建议: Bearer Token可能无效`);
                            } else if (result.error.includes('429')) {
                                console.log(`   💡 建议: API调用频率过高，请稍后重试`);
                            }
                        }
                    } catch (parseError) {
                        console.log(`   ❌ JSON解析错误: ${parseError.message}`);
                        console.log(`   原始输出: ${output.substring(0, 200)}...`);
                    }
                } else {
                    console.log(`   ❌ 无输出`);
                }
                
                resolve();
            });
            
            child.on('error', (err) => {
                console.log(`   ❌ 进程错误: ${err.message}`);
                resolve();
            });
            
            // 发送测试数据
            child.stdin.write(JSON.stringify(testCase.input));
            child.stdin.end();
        });
        
        // 在测试之间添加延迟
        if (i < testCases.length - 1) {
            console.log('   ⏳ 等待2秒后进行下一个测试...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n📋 总结:');
    console.log('如果测试成功，说明插件工作正常');
    console.log('如果遇到网络错误，可能需要：');
    console.log('1. 检查网络连接');
    console.log('2. 确认防火墙设置');
    console.log('3. 验证Bearer Token有效性');
    console.log('4. 检查是否需要代理设置');
}

finalTest().catch(console.error);
