# Wolfram Alpha API 配置
# 获取 App ID: https://products.wolframalpha.com/api/
WOLFRAM_APP_ID=your_wolfram_app_id_here

# 输出格式配置
# 可选值: plaintext, image, mathml, sound
# 可以组合使用，用逗号分隔，如: plaintext,image
WOLFRAM_OUTPUT_FORMAT=plaintext,image

# 单位系统
# metric: 公制单位 (米、千克、摄氏度等)
# imperial: 英制单位 (英尺、磅、华氏度等)
WOLFRAM_UNITS=metric

# API 请求超时时间（秒）
WOLFRAM_TIMEOUT=20

# 是否保存图表和图像到本地 (true/false)
SAVE_IMAGES=true

# 图像保存路径 (相对于项目根目录)
IMAGE_SAVE_PATH=image/wolfram
