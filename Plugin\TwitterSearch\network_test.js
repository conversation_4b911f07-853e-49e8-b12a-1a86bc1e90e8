const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function networkTest() {
    console.log('🌐 网络连接诊断测试');
    console.log('====================');
    
    // 读取Bearer <PERSON>
    let bearerToken = null;
    const configPath = path.join(__dirname, 'config.env');
    
    if (fs.existsSync(configPath)) {
        const config = fs.readFileSync(configPath, 'utf8');
        const lines = config.split('\n');
        for (const line of lines) {
            if (line.startsWith('TWITTER_BEARER_TOKEN=')) {
                bearerToken = line.split('=')[1].trim().replace(/['"]/g, '');
                break;
            }
        }
    }
    
    if (!bearerToken) {
        console.log('❌ 未找到Bearer Token');
        return;
    }
    
    console.log('✅ Bearer Token已加载');
    
    // 测试1: 基本网络连接
    console.log('\n📋 测试1: 基本网络连接');
    try {
        const response = await axios.get('https://httpbin.org/get', { timeout: 10000 });
        console.log('✅ 基本网络连接正常');
    } catch (error) {
        console.log('❌ 基本网络连接失败:', error.message);
        return;
    }
    
    // 测试2: HTTPS连接
    console.log('\n📋 测试2: HTTPS连接测试');
    try {
        const response = await axios.get('https://api.github.com', { timeout: 10000 });
        console.log('✅ HTTPS连接正常');
    } catch (error) {
        console.log('❌ HTTPS连接失败:', error.message);
    }
    
    // 测试3: Twitter域名解析
    console.log('\n📋 测试3: Twitter API域名连接');
    try {
        // 先测试简单的HEAD请求
        const response = await axios.head('https://api.twitter.com', { 
            timeout: 15000,
            validateStatus: () => true // 接受所有状态码
        });
        console.log('✅ Twitter API域名可达，状态码:', response.status);
    } catch (error) {
        console.log('❌ Twitter API域名连接失败:', error.message);
        if (error.code === 'ENOTFOUND') {
            console.log('💡 可能是DNS解析问题');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('💡 可能是网络超时或防火墙阻止');
        }
        return;
    }
    
    // 测试4: Twitter API认证测试
    console.log('\n📋 测试4: Twitter API认证测试');
    try {
        const response = await axios.get('https://api.twitter.com/2/tweets/search/recent', {
            headers: {
                'Authorization': `Bearer ${bearerToken}`,
                'Content-Type': 'application/json'
            },
            params: {
                query: 'hello',
                max_results: 10
            },
            timeout: 30000
        });
        
        console.log('🎉 Twitter API测试成功！');
        console.log('📊 状态码:', response.status);
        console.log('📊 找到推文:', response.data.data?.length || 0, '条');
        
        if (response.data.data && response.data.data.length > 0) {
            console.log('📝 第一条推文:', response.data.data[0].text.substring(0, 50) + '...');
        }
        
    } catch (error) {
        console.log('❌ Twitter API测试失败:');
        
        if (error.response) {
            console.log('   HTTP状态:', error.response.status);
            console.log('   错误详情:', JSON.stringify(error.response.data, null, 2));
            
            switch (error.response.status) {
                case 401:
                    console.log('💡 Bearer Token无效或已过期');
                    break;
                case 403:
                    console.log('💡 访问被拒绝，可能是权限问题');
                    break;
                case 429:
                    console.log('💡 API调用频率过高');
                    break;
                default:
                    console.log('💡 其他API错误');
            }
        } else {
            console.log('   网络错误:', error.message);
            console.log('   错误代码:', error.code);
            
            if (error.code === 'ETIMEDOUT') {
                console.log('💡 连接超时，可能需要代理或VPN');
            } else if (error.code === 'ECONNRESET') {
                console.log('💡 连接被重置，可能是防火墙问题');
            }
        }
    }
    
    console.log('\n🏁 网络诊断完成');
}

networkTest().catch(console.error);
