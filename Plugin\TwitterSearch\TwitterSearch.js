const axios = require('axios');
const fs = require('fs');
const path = require('path');

class TwitterSearch {
    constructor() {
        this.bearerToken = null;
        this.baseURL = 'https://api.twitter.com/2';
        this.loadConfig();
    }

    loadConfig() {
        try {
            // 尝试从插件目录加载配置
            const configPath = path.join(__dirname, 'config.env');

            if (fs.existsSync(configPath)) {
                const config = fs.readFileSync(configPath, 'utf8');
                const lines = config.split('\n');
                for (const line of lines) {
                    if (line.startsWith('TWITTER_BEARER_TOKEN=')) {
                        this.bearerToken = line.split('=')[1].trim().replace(/['"]/g, '');
                        break;
                    }
                }
            }

            // 如果插件配置中没有，尝试从环境变量获取
            if (!this.bearerToken) {
                this.bearerToken = process.env.TWITTER_BEARER_TOKEN;
            }
        } catch (error) {
            console.error('Error loading Twitter config:', error);
        }
    }

    async makeRequest(endpoint, params = {}) {
        if (!this.bearerToken) {
            throw new Error('Twitter Bearer Token not configured. Please set TWITTER_BEARER_TOKEN in config.');
        }

        try {
            const response = await axios.get(`${this.baseURL}${endpoint}`, {
                headers: {
                    'Authorization': `Bearer ${this.bearerToken}`,
                    'Content-Type': 'application/json'
                },
                params: params
            });

            return response.data;
        } catch (error) {
            if (error.response) {
                throw new Error(`Twitter API Error: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
            } else {
                throw new Error(`Network Error: ${error.message}`);
            }
        }
    }

    async searchTweets(query, options = {}) {
        const params = {
            query: query,
            max_results: Math.min(Math.max(options.max_results || 10, 10), 100),
            'tweet.fields': options.tweet_fields || 'author_id,created_at,public_metrics,lang,context_annotations',
            'user.fields': options.user_fields || 'username,name,verified,public_metrics',
            expansions: 'author_id'
        };

        if (options.start_time) {
            params.start_time = options.start_time;
        }
        if (options.end_time) {
            params.end_time = options.end_time;
        }

        const data = await this.makeRequest('/tweets/search/recent', params);
        
        // 处理用户信息映射
        const users = {};
        if (data.includes && data.includes.users) {
            data.includes.users.forEach(user => {
                users[user.id] = user;
            });
        }

        // 合并推文和用户信息
        const tweets = data.data || [];
        const processedTweets = tweets.map(tweet => {
            const user = users[tweet.author_id] || {};
            return {
                id: tweet.id,
                text: tweet.text,
                created_at: tweet.created_at,
                author: {
                    id: tweet.author_id,
                    username: user.username || 'unknown',
                    name: user.name || 'Unknown User',
                    verified: user.verified || false,
                    followers_count: user.public_metrics?.followers_count || 0,
                    following_count: user.public_metrics?.following_count || 0
                },
                metrics: {
                    retweet_count: tweet.public_metrics?.retweet_count || 0,
                    like_count: tweet.public_metrics?.like_count || 0,
                    reply_count: tweet.public_metrics?.reply_count || 0,
                    quote_count: tweet.public_metrics?.quote_count || 0
                },
                lang: tweet.lang,
                url: `https://twitter.com/${user.username}/status/${tweet.id}`
            };
        });

        return {
            tweets: processedTweets,
            meta: data.meta || {},
            result_count: processedTweets.length
        };
    }

    async getUserInfo(username, options = {}) {
        const params = {
            'user.fields': options.user_fields || 'created_at,description,public_metrics,verified,location,url'
        };

        const data = await this.makeRequest(`/users/by/username/${username}`, params);
        
        if (!data.data) {
            throw new Error(`User @${username} not found`);
        }

        const user = data.data;
        return {
            id: user.id,
            username: user.username,
            name: user.name,
            description: user.description || '',
            created_at: user.created_at,
            verified: user.verified || false,
            location: user.location || '',
            url: user.url || '',
            profile_url: `https://twitter.com/${user.username}`,
            metrics: {
                followers_count: user.public_metrics?.followers_count || 0,
                following_count: user.public_metrics?.following_count || 0,
                tweet_count: user.public_metrics?.tweet_count || 0,
                listed_count: user.public_metrics?.listed_count || 0
            }
        };
    }

    async getUserTweets(username, options = {}) {
        // 首先获取用户ID
        const userInfo = await this.getUserInfo(username);
        const userId = userInfo.id;

        const params = {
            max_results: Math.min(Math.max(options.max_results || 10, 5), 100),
            'tweet.fields': 'created_at,public_metrics,lang,context_annotations',
            exclude: options.exclude || 'retweets'
        };

        const data = await this.makeRequest(`/users/${userId}/tweets`, params);
        
        const tweets = data.data || [];
        const processedTweets = tweets.map(tweet => ({
            id: tweet.id,
            text: tweet.text,
            created_at: tweet.created_at,
            author: {
                username: username,
                name: userInfo.name
            },
            metrics: {
                retweet_count: tweet.public_metrics?.retweet_count || 0,
                like_count: tweet.public_metrics?.like_count || 0,
                reply_count: tweet.public_metrics?.reply_count || 0,
                quote_count: tweet.public_metrics?.quote_count || 0
            },
            lang: tweet.lang,
            url: `https://twitter.com/${username}/status/${tweet.id}`
        }));

        return {
            user: userInfo,
            tweets: processedTweets,
            meta: data.meta || {},
            result_count: processedTweets.length
        };
    }
}

// 主执行函数
async function main() {
    try {
        const input = process.stdin;
        let data = '';
        
        input.on('data', chunk => {
            data += chunk;
        });
        
        input.on('end', async () => {
            try {
                const params = JSON.parse(data.trim());
                const twitter = new TwitterSearch();
                
                let result;
                
                switch (params.command) {
                    case 'search_tweets':
                        if (!params.query) {
                            throw new Error('Query parameter is required for tweet search');
                        }
                        result = await twitter.searchTweets(params.query, params);
                        break;
                        
                    case 'get_user_info':
                        if (!params.username) {
                            throw new Error('Username parameter is required');
                        }
                        result = await twitter.getUserInfo(params.username, params);
                        break;
                        
                    case 'get_user_tweets':
                        if (!params.username) {
                            throw new Error('Username parameter is required');
                        }
                        result = await twitter.getUserTweets(params.username, params);
                        break;
                        
                    default:
                        throw new Error(`Unknown command: ${params.command}`);
                }
                
                console.log(JSON.stringify({
                    status: 'success',
                    result: result,
                    messageForAI: `Twitter ${params.command} completed successfully. Found ${result.result_count || result.tweets?.length || 1} results.`
                }));
                
            } catch (error) {
                console.log(JSON.stringify({
                    status: 'error',
                    error: error.message,
                    messageForAI: `Twitter API error: ${error.message}`
                }));
            }
        });
        
    } catch (error) {
        console.log(JSON.stringify({
            status: 'error',
            error: error.message,
            messageForAI: `Twitter plugin initialization error: ${error.message}`
        }));
    }
}

if (require.main === module) {
    main();
}

module.exports = TwitterSearch;
