# 🚀 Unsplash Search Plugin 快速启动指南

## 📦 安装步骤

### 1. 获取 Unsplash API Key

1. 访问 [Unsplash Developers](https://unsplash.com/developers)
2. 点击 "Register as a developer"
3. 创建新应用 (New Application)
4. 复制 Access Key

### 2. 配置插件

运行安装向导：
```bash
cd Plugin/UnsplashSearch
node install.js
```

或手动配置：
```bash
cp config.env.example config.env
# 编辑 config.env 文件，填入您的 API Key
```

### 3. 重启 VCP 服务器

```bash
# 在项目根目录
node server.js
```

### 4. 更新系统提示词

在您的 AI 系统提示词中添加：
```
{{VCPUnsplashSearch}}
```

## 🧪 测试插件

```bash
cd Plugin/UnsplashSearch
node test.js
```

## 🎯 快速使用

### 基础搜索
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」sunset mountain「末」,
per_page:「始」3「末」
<<<[END_TOOL_REQUEST]>>>
```

### 高级搜索
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」UnsplashSearch「末」,
query:「始」城市夜景「末」,
per_page:「始」5「末」,
orientation:「始」landscape「末」,
category:「始」places「末」,
color:「始」blue「末」
<<<[END_TOOL_REQUEST]>>>
```

## 📊 API 限制

- **免费账户**: 每小时 50 次请求
- **演示账户**: 每小时 50 次请求
- **生产账户**: 每小时 5000 次请求

## 🔧 常见问题

### Q: 401 Unauthorized 错误
A: 检查 Access Key 是否正确配置

### Q: 403 Rate Limit 错误  
A: 超出每小时请求限制，请稍后重试

### Q: 网络连接错误
A: 检查网络连接和防火墙设置

### Q: 图片下载失败
A: 确保有写入权限，检查保存路径

## 📚 更多资源

- [完整文档](README.md)
- [使用示例](examples.md)
- [Unsplash API 文档](https://unsplash.com/documentation)
- [VCPToolBox 项目](https://github.com/lioensky/VCPToolBox)

## 🆘 获取帮助

如果遇到问题：
1. 查看 VCP 服务器日志
2. 运行测试脚本诊断
3. 检查网络连接
4. 验证 API Key 有效性

---

🎉 **恭喜！您现在可以让 AI 搜索和使用高质量的 Unsplash 图片了！**
