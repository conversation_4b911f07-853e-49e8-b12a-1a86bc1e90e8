const { spawn } = require('child_process');
const path = require('path');

// 测试数据
const testCases = [
    {
        name: "搜索推文测试",
        input: {
            command: "search_tweets",
            query: "#AI OR artificial intelligence",
            max_results: 10
        }
    },
    {
        name: "获取用户信息测试",
        input: {
            command: "get_user_info",
            username: "openai"
        }
    },
    {
        name: "获取用户推文测试",
        input: {
            command: "get_user_tweets",
            username: "openai",
            max_results: 5,
            exclude: "retweets"
        }
    }
];

async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 运行测试: ${testCase.name}`);
        console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));
        
        const child = spawn('node', ['TwitterSearch.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let output = '';
        let error = '';
        
        child.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        child.stderr.on('data', (data) => {
            error += data.toString();
        });
        
        child.on('close', (code) => {
            try {
                if (error) {
                    console.log(`❌ 错误输出:`, error);
                }
                
                if (output) {
                    const result = JSON.parse(output.trim());
                    console.log(`✅ 测试结果:`, JSON.stringify(result, null, 2));
                    
                    if (result.status === 'success') {
                        console.log(`🎉 ${testCase.name} - 成功`);
                    } else {
                        console.log(`⚠️ ${testCase.name} - 失败: ${result.error}`);
                    }
                } else {
                    console.log(`❌ ${testCase.name} - 无输出`);
                }
                
                resolve();
            } catch (parseError) {
                console.log(`❌ ${testCase.name} - JSON解析错误:`, parseError.message);
                console.log(`原始输出:`, output);
                resolve();
            }
        });
        
        child.on('error', (err) => {
            console.log(`❌ ${testCase.name} - 进程错误:`, err.message);
            resolve();
        });
        
        // 发送测试数据
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

async function runAllTests() {
    console.log('🚀 开始Twitter API插件测试');
    console.log('📋 注意: 需要在config.env中配置有效的TWITTER_BEARER_TOKEN');
    
    for (const testCase of testCases) {
        await runTest(testCase);
        // 添加延迟以避免速率限制
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✨ 所有测试完成');
    console.log('\n📖 使用说明:');
    console.log('1. 确保已配置Twitter Bearer Token');
    console.log('2. 免费版本限制: 500,000推文/月');
    console.log('3. 搜索范围: 最近7天的推文');
    console.log('4. 速率限制: 300请求/15分钟');
}

// 检查配置文件
const fs = require('fs');
const configPath = path.join(__dirname, 'config.env');

if (!fs.existsSync(configPath)) {
    console.log('⚠️ 警告: 未找到config.env文件');
    console.log('📝 请复制config.env.example为config.env并配置Twitter Bearer Token');
    console.log('🔗 获取Token: https://developer.twitter.com/');
} else {
    const config = fs.readFileSync(configPath, 'utf8');
    if (!config.includes('TWITTER_BEARER_TOKEN=') || config.includes('your_bearer_token_here')) {
        console.log('⚠️ 警告: Twitter Bearer Token未配置');
        console.log('📝 请在config.env中设置有效的TWITTER_BEARER_TOKEN');
    }
}

if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runTest, runAllTests };
